{% extends "base.html" %}

{% block title %}车间异常记录 - 品质中心管理系统{% endblock %}

{% block extra_css %}
<style>
    .filter-section {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    
    .filter-row {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 10px;
    }
    
    .filter-item {
        margin-right: 15px;
        margin-bottom: 10px;
    }
    
    .filter-label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
    }
    
    .pagination-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 20px;
    }
    
    .status-badge {
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .status-pending {
        background-color: #ffeeba;
        color: #856404;
    }
    
    .status-processing {
        background-color: #b8daff;
        color: #004085;
    }
    
    .status-completed {
        background-color: #c3e6cb;
        color: #155724;
    }
    
    .status-closed {
        background-color: #d6d8db;
        color: #383d41;
    }
    
    .table-responsive {
        overflow-x: auto;
    }
    
    .btn-filter {
        background-color: #007bff;
        color: white;
    }
    
    .btn-reset {
        background-color: #6c757d;
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <h1>车间异常记录</h1>
</div>

<div class="content-container">
    <!-- 筛选区域 -->
    <div class="filter-section">
        <form method="get" action="{{ url_for('process_control.workshop_abnormal') }}">
            <div class="filter-row">
                <div class="filter-item">
                    <label class="filter-label">记录日期范围</label>
                    <div class="date-range">
                        <input type="date" name="start_date" value="{{ start_date }}" class="form-control">
                        <span>至</span>
                        <input type="date" name="end_date" value="{{ end_date }}" class="form-control">
                    </div>
                </div>
                <div class="filter-item">
                    <label class="filter-label">产品编号</label>
                    <input type="text" name="product_number" value="{{ product_number }}" class="form-control" placeholder="输入产品编号">
                </div>
                <div class="filter-item">
                    <label class="filter-label">车间</label>
                    <select name="workshop" class="form-control">
                        <option value="">全部</option>
                        {% for ws in workshops %}
                        <option value="{{ ws }}" {% if workshop == ws %}selected{% endif %}>{{ ws }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="filter-item">
                    <label class="filter-label">异常类型</label>
                    <select name="abnormal_type" class="form-control">
                        <option value="">全部</option>
                        {% for type in abnormal_types %}
                        <option value="{{ type }}" {% if abnormal_type == type %}selected{% endif %}>{{ type }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="filter-row">
                <div class="filter-item">
                    <button type="submit" class="btn btn-filter">筛选</button>
                    <a href="{{ url_for('process_control.workshop_abnormal') }}" class="btn btn-reset">重置</a>
                    <a href="{{ url_for('process_control.new_abnormal') }}" class="btn btn-primary">新增异常记录</a>
                </div>
            </div>
        </form>
    </div>

    <!-- 记录表格 -->
    <div class="table-responsive">
        <table class="table table-striped table-hover">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>记录日期</th>
                    <th>产品编号</th>
                    <th>产品名称</th>
                    <th>车间</th>
                    <th>工序</th>
                    <th>异常类型</th>
                    <th>状态</th>
                    <th>记录人</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                {% for record in records %}
                <tr>
                    <td>{{ record.id }}</td>
                    <td>{{ record.record_date }}</td>
                    <td>{{ record.product_number }}</td>
                    <td>{{ record.product_name }}</td>
                    <td>{{ record.workshop }}</td>
                    <td>{{ record.process_name }}</td>
                    <td>{{ record.abnormal_type }}</td>
                    <td>
                        <span class="status-badge 
                            {% if record.status == '待处理' %}status-pending
                            {% elif record.status == '处理中' %}status-processing
                            {% elif record.status == '已完成' %}status-completed
                            {% else %}status-closed{% endif %}">
                            {{ record.status }}
                        </span>
                    </td>
                    <td>{{ record.recorder }}</td>
                    <td>
                        <button class="btn btn-sm btn-info view-details" data-id="{{ record.id }}">详情</button>
                        {% if record.status != '已关闭' %}
                        <button class="btn btn-sm btn-warning update-status" data-id="{{ record.id }}">更新状态</button>
                        {% endif %}
                    </td>
                </tr>
                {% else %}
                <tr>
                    <td colspan="10" class="text-center">没有找到符合条件的记录</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- 分页控件 -->
    <div class="pagination-container">
        <div class="pagination-info">
            显示 {{ records|length }} 条记录，共 {{ total_records }} 条
        </div>
        <nav aria-label="Page navigation">
            <ul class="pagination">
                {% if page > 1 %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('process_control.workshop_abnormal', page=page-1, per_page=per_page, start_date=start_date, end_date=end_date, product_number=product_number, workshop=workshop, abnormal_type=abnormal_type) }}">上一页</a>
                </li>
                {% endif %}
                
                {% for p in range(max(1, page-2), min(total_pages+1, page+3)) %}
                <li class="page-item {% if p == page %}active{% endif %}">
                    <a class="page-link" href="{{ url_for('process_control.workshop_abnormal', page=p, per_page=per_page, start_date=start_date, end_date=end_date, product_number=product_number, workshop=workshop, abnormal_type=abnormal_type) }}">{{ p }}</a>
                </li>
                {% endfor %}
                
                {% if page < total_pages %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('process_control.workshop_abnormal', page=page+1, per_page=per_page, start_date=start_date, end_date=end_date, product_number=product_number, workshop=workshop, abnormal_type=abnormal_type) }}">下一页</a>
                </li>
                {% endif %}
            </ul>
        </nav>
    </div>
</div>

<!-- 详情模态框 -->
<div class="modal fade" id="detailsModal" tabindex="-1" role="dialog" aria-labelledby="detailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="detailsModalLabel">异常记录详情</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="detailsModalBody">
                <!-- 详情内容将通过AJAX加载 -->
                <div class="loading">加载中...</div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 更新状态模态框 -->
<div class="modal fade" id="statusModal" tabindex="-1" role="dialog" aria-labelledby="statusModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="statusModalLabel">更新异常记录状态</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="updateStatusForm">
                    <input type="hidden" id="recordId" name="recordId">
                    <div class="form-group">
                        <label for="status">选择状态</label>
                        <select class="form-control" id="status" name="status">
                            <option value="待处理">待处理</option>
                            <option value="处理中">处理中</option>
                            <option value="已完成">已完成</option>
                            <option value="已关闭">已关闭</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveStatus">保存</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // 查看详情
        $('.view-details').click(function() {
            const recordId = $(this).data('id');
            $('#detailsModalBody').html('<div class="loading">加载中...</div>');
            $('#detailsModal').modal('show');
            
            // 加载详情
            $.ajax({
                url: `/process/api/abnormal_details/${recordId}`,
                method: 'GET',
                success: function(response) {
                    if (response.success) {
                        const record = response.record;
                        let detailsHtml = `
                            <div class="record-details">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>产品编号:</strong> ${record.product_number}</p>
                                        <p><strong>产品名称:</strong> ${record.product_name}</p>
                                        <p><strong>车间:</strong> ${record.workshop}</p>
                                        <p><strong>工序:</strong> ${record.process_name}</p>
                                        <p><strong>异常类型:</strong> ${record.abnormal_type}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>记录日期:</strong> ${record.record_date}</p>
                                        <p><strong>记录人:</strong> ${record.recorder}</p>
                                        <p><strong>状态:</strong> ${record.status}</p>
                                        <p><strong>创建时间:</strong> ${record.created_at}</p>
                                        <p><strong>更新时间:</strong> ${record.updated_at || '无'}</p>
                                    </div>
                                </div>
                                <hr>
                                <div class="row">
                                    <div class="col-12">
                                        <h5>异常详情</h5>
                                        <div class="abnormal-details">`;
                        
                        // 处理异常详情
                        if (record.abnormal_details && record.abnormal_details.length > 0) {
                            detailsHtml += '<ul>';
                            record.abnormal_details.forEach(detail => {
                                detailsHtml += `<li>${detail.description || detail}</li>`;
                            });
                            detailsHtml += '</ul>';
                        } else {
                            detailsHtml += '<p>无详细信息</p>';
                        }
                        
                        detailsHtml += `</div>
                                    </div>
                                </div>`;
                                
                        // 显示图片（如果有）
                        if (record.image_path) {
                            detailsHtml += `
                                <hr>
                                <div class="row">
                                    <div class="col-12">
                                        <h5>相关图片</h5>
                                        <img src="/${record.image_path}" class="img-fluid" alt="异常图片">
                                    </div>
                                </div>`;
                        }
                        
                        // 备注信息
                        detailsHtml += `
                            <hr>
                            <div class="row">
                                <div class="col-12">
                                    <h5>备注</h5>
                                    <p>${record.remarks || '无'}</p>
                                </div>
                            </div>
                        </div>`;
                        
                        $('#detailsModalBody').html(detailsHtml);
                    } else {
                        $('#detailsModalBody').html(`<div class="alert alert-danger">${response.error}</div>`);
                    }
                },
                error: function() {
                    $('#detailsModalBody').html('<div class="alert alert-danger">加载详情失败</div>');
                }
            });
        });
        
        // 更新状态
        $('.update-status').click(function() {
            const recordId = $(this).data('id');
            $('#recordId').val(recordId);
            $('#statusModal').modal('show');
        });
        
        // 保存状态
        $('#saveStatus').click(function() {
            const recordId = $('#recordId').val();
            const status = $('#status').val();
            
            $.ajax({
                url: `/process/api/update_abnormal_status/${recordId}`,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ status: status }),
                success: function(response) {
                    if (response.success) {
                        $('#statusModal').modal('hide');
                        // 刷新页面以显示更新后的状态
                        location.reload();
                    } else {
                        alert(`更新失败: ${response.error}`);
                    }
                },
                error: function() {
                    alert('更新状态请求失败');
                }
            });
        });
    });
</script>
{% endblock %} 