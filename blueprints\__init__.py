# 蓝图包初始化文件 

def register_blueprints(app):
    """注册所有蓝图"""
    from blueprints.incoming_inspection import incoming_inspection_bp, full_inspection_bp, sampling_inspection_bp
    from blueprints.process_control import process_control_bp
    
    # 注册所有蓝图
    app.register_blueprint(incoming_inspection_bp)
    app.register_blueprint(full_inspection_bp)
    app.register_blueprint(sampling_inspection_bp)
    app.register_blueprint(process_control_bp) 