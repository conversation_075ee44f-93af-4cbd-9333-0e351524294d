#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复API问题
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from db_config import get_db_connection

def fix_materials_table():
    """修复materials表，添加unit字段"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        print("🔧 检查materials表结构...")
        
        # 检查字段是否已存在
        cursor.execute("DESCRIBE materials")
        existing_columns = [column[0] for column in cursor.fetchall()]
        print(f"现有字段: {existing_columns}")
        
        if 'unit' not in existing_columns:
            print("🔧 添加unit字段...")
            cursor.execute("""
                ALTER TABLE materials 
                ADD COLUMN unit VARCHAR(20) DEFAULT '个' 
                COMMENT '单位：个、kg、m、L等'
            """)
            print("✅ unit字段添加成功")
        else:
            print("⚠️ unit字段已存在")
        
        # 更新现有数据的单位
        print("🔧 更新现有数据的单位...")
        cursor.execute("UPDATE materials SET unit = '个' WHERE unit IS NULL OR unit = ''")
        affected_rows = cursor.rowcount
        print(f"✅ 更新了 {affected_rows} 条记录的单位信息")
        
        # 提交更改
        conn.commit()
        
        # 验证修复
        cursor.execute("SELECT material_number, material_name, unit FROM materials LIMIT 5")
        materials = cursor.fetchall()
        print("\n📋 前5条物料信息:")
        for material in materials:
            print(f"  - {material[0]}: {material[1]} ({material[2]})")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def test_api():
    """测试物料信息API"""
    try:
        import requests
        
        print("\n🧪 测试物料信息API...")
        
        # 测试API
        url = "http://localhost:5000/api/material_info/TEST001"
        print(f"请求URL: {url}")
        
        response = requests.get(url, timeout=5)
        print(f"响应状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API测试成功")
            print(f"返回数据: {data}")
        else:
            print(f"❌ API测试失败: {response.status_code}")
            print(f"错误信息: {response.text}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始快速修复API问题")
    print("=" * 50)
    
    # 1. 修复数据库
    if not fix_materials_table():
        print("❌ 数据库修复失败")
        return False
    
    # 2. 测试API
    if not test_api():
        print("❌ API仍然有问题，请检查Flask应用是否运行")
        return False
    
    print("=" * 50)
    print("🎉 API修复完成！")
    print("现在可以测试物料信息自动获取功能了")
    
    return True

if __name__ == "__main__":
    main()
