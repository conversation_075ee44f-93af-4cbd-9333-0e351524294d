{% extends "base.html" %}

{% block title %}系统设置 - 品质中心管理系统{% endblock %}

{% block extra_css %}
<style>
    body {
        background: #1a1a1a;
        color: #e0e0e0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        margin: 0;
        padding: 0;
    }

    .settings-layout {
        display: flex;
        min-height: 100vh;
        background: #1a1a1a;
    }

    .settings-sidebar {
        width: 240px;
        background: #2d2d2d;
        border-right: 1px solid #404040;
        padding: 20px 0;
        position: fixed;
        height: 100vh;
        overflow-y: auto;
    }

    .sidebar-header {
        padding: 0 20px 20px;
        border-bottom: 1px solid #404040;
        margin-bottom: 20px;
    }

    .sidebar-title {
        font-size: 16px;
        font-weight: 600;
        color: #ffffff;
        margin: 0;
    }

    .sidebar-subtitle {
        font-size: 12px;
        color: #888;
        margin: 4px 0 0;
    }

    .sidebar-nav {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .nav-item {
        margin: 2px 0;
    }

    .nav-link {
        display: flex;
        align-items: center;
        padding: 12px 20px;
        color: #b0b0b0;
        text-decoration: none;
        font-size: 14px;
        transition: all 0.2s ease;
        border-left: 3px solid transparent;
    }

    .nav-link:hover {
        background: #3a3a3a;
        color: #ffffff;
    }

    .nav-link.active {
        background: #3a3a3a;
        color: #ffffff;
        border-left-color: #4a9eff;
    }

    .nav-link i {
        margin-right: 12px;
        width: 16px;
        font-size: 14px;
    }

    .settings-content {
        flex: 1;
        margin-left: 240px;
        padding: 40px;
        background: #1a1a1a;
    }

    .content-header {
        margin-bottom: 30px;
    }

    .content-title {
        font-size: 24px;
        font-weight: 600;
        color: #ffffff;
        margin: 0 0 8px;
    }

    .content-subtitle {
        font-size: 14px;
        color: #888;
        margin: 0;
    }

    .settings-group {
        background: #2d2d2d;
        border-radius: 8px;
        border: 1px solid #404040;
        margin-bottom: 20px;
        overflow: hidden;
    }

    .setting-item {
        padding: 20px;
        border-bottom: 1px solid #404040;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
    }

    .setting-item:last-child {
        border-bottom: none;
    }

    .setting-info {
        flex: 1;
        margin-right: 20px;
    }

    .setting-title {
        font-size: 15px;
        font-weight: 500;
        color: #ffffff;
        margin: 0 0 4px;
    }

    .setting-description {
        font-size: 13px;
        color: #888;
        margin: 0;
        line-height: 1.4;
    }

    .setting-control {
        flex-shrink: 0;
        min-width: 200px;
    }

    .form-input {
        width: 100%;
        height: 36px;
        padding: 8px 12px;
        font-size: 13px;
        background: #404040;
        border: 1px solid #555;
        border-radius: 6px;
        color: #ffffff;
        transition: all 0.2s ease;
        box-sizing: border-box;
    }

    .form-input:focus {
        outline: none;
        border-color: #4a9eff;
        background: #4a4a4a;
    }

    .form-input::placeholder {
        color: #888;
    }

    select.form-input {
        background: #404040;
        border: 1px solid #555;
        border-radius: 6px;
        color: #ffffff;
        padding: 8px 12px;
        font-size: 13px;
        cursor: pointer;
    }

    select.form-input:focus {
        outline: none;
        border-color: #4a9eff;
        background: #4a4a4a;
    }

    input[type="checkbox"] {
        width: 16px;
        height: 16px;
        accent-color: #4a9eff;
        cursor: pointer;
    }

    label {
        color: #e0e0e0;
        cursor: pointer;
    }

    label:hover {
        color: #ffffff;
    }

    .toggle-switch {
        position: relative;
        width: 44px;
        height: 24px;
        background: #555;
        border-radius: 12px;
        cursor: pointer;
        transition: background 0.2s ease;
    }

    .toggle-switch.active {
        background: #4a9eff;
    }

    .toggle-switch::after {
        content: '';
        position: absolute;
        top: 2px;
        left: 2px;
        width: 20px;
        height: 20px;
        background: #ffffff;
        border-radius: 50%;
        transition: transform 0.2s ease;
    }

    .toggle-switch.active::after {
        transform: translateX(20px);
    }

    .select-dropdown {
        position: relative;
        width: 100%;
    }

    .select-button {
        width: 100%;
        height: 36px;
        padding: 8px 12px;
        background: #404040;
        border: 1px solid #555;
        border-radius: 6px;
        color: #ffffff;
        font-size: 13px;
        text-align: left;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        transition: all 0.2s ease;
    }

    .select-button:hover {
        background: #4a4a4a;
        border-color: #666;
    }

    .select-button i {
        color: #888;
    }

    .path-browser {
        display: flex;
        gap: 8px;
        align-items: center;
    }

    .path-browser input {
        flex: 1;
    }

    .browse-btn {
        padding: 8px 12px;
        font-size: 12px;
        background: #4a9eff;
        color: white;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        white-space: nowrap;
        transition: background 0.2s ease;
    }

    .browse-btn:hover {
        background: #3a8eef;
    }

    .btn-group {
        display: flex;
        gap: 12px;
        justify-content: flex-end;
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #404040;
    }

    .btn {
        padding: 10px 20px;
        font-size: 13px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s ease;
        font-weight: 500;
    }

    .btn-primary {
        background: #4a9eff;
        color: white;
    }

    .btn-primary:hover {
        background: #3a8eef;
    }

    .btn-secondary {
        background: #555;
        color: #e0e0e0;
    }

    .btn-secondary:hover {
        background: #666;
    }

    .alert {
        padding: 12px 16px;
        margin-bottom: 20px;
        border-radius: 6px;
        font-size: 13px;
        border: 1px solid;
    }

    .alert-success {
        background-color: rgba(76, 175, 80, 0.1);
        border-color: #4caf50;
        color: #4caf50;
    }

    .alert-error {
        background-color: rgba(244, 67, 54, 0.1);
        border-color: #f44336;
        color: #f44336;
    }

    /* 标签页内容 */
    .tab-content {
        display: none;
    }

    .tab-content.active {
        display: block;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .settings-sidebar {
            transform: translateX(-100%);
            transition: transform 0.3s ease;
        }

        .settings-content {
            margin-left: 0;
            padding: 20px;
        }

        .setting-item {
            flex-direction: column;
            align-items: stretch;
        }

        .setting-info {
            margin-right: 0;
            margin-bottom: 15px;
        }

        .setting-control {
            min-width: auto;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="settings-layout">
    <!-- 左侧导航栏 -->
    <div class="settings-sidebar">
        <div class="sidebar-header">
            <h2 class="sidebar-title">系统设置</h2>
            <p class="sidebar-subtitle">配置系统参数</p>
        </div>

        <ul class="sidebar-nav">
            <li class="nav-item">
                <a href="#general" class="nav-link active" data-tab="general">
                    <i class="fas fa-cog"></i>
                    常规设置
                </a>
            </li>
            <li class="nav-item">
                <a href="#storage" class="nav-link" data-tab="storage">
                    <i class="fas fa-folder"></i>
                    存储路径
                </a>
            </li>
            <li class="nav-item">
                <a href="#system" class="nav-link" data-tab="system">
                    <i class="fas fa-cogs"></i>
                    系统参数
                </a>
            </li>
            <li class="nav-item">
                <a href="#security" class="nav-link" data-tab="security">
                    <i class="fas fa-shield-alt"></i>
                    安全设置
                </a>
            </li>
        </ul>
    </div>

    <!-- 右侧内容区域 -->
    <div class="settings-content">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'success' if category == 'success' else 'error' }}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <form method="POST" action="{{ url_for('system_settings.save_settings') }}">
            <!-- 常规设置 -->
            <div id="general-tab" class="tab-content active">
                <div class="content-header">
                    <h1 class="content-title">常规设置</h1>
                    <p class="content-subtitle">配置系统的基本参数和行为</p>
                </div>

                <div class="settings-group">
                    <div class="setting-item">
                        <div class="setting-info">
                            <h3 class="setting-title">系统模式</h3>
                            <p class="setting-description">选择系统运行模式，影响功能可用性和性能</p>
                        </div>
                        <div class="setting-control">
                            <div class="select-dropdown">
                                <button type="button" class="select-button">
                                    <span>生产模式</span>
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="setting-item">
                        <div class="setting-info">
                            <h3 class="setting-title">自动保存</h3>
                            <p class="setting-description">在编辑检验记录时自动保存草稿</p>
                        </div>
                        <div class="setting-control">
                            <div class="toggle-switch active" onclick="toggleSwitch(this)"></div>
                        </div>
                    </div>

                    <div class="setting-item">
                        <div class="setting-info">
                            <h3 class="setting-title">数据备份</h3>
                            <p class="setting-description">定期自动备份重要数据</p>
                        </div>
                        <div class="setting-control">
                            <div class="toggle-switch active" onclick="toggleSwitch(this)"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 存储路径设置 -->
            <div id="storage-tab" class="tab-content">
                <div class="content-header">
                    <h1 class="content-title">存储路径</h1>
                    <p class="content-subtitle">配置文档和图片文件的存储位置，确保数据安全和便于管理</p>
                </div>

                <!-- 路径配置说明 -->
                <div class="settings-group" style="background: #1a3d1a; border-color: #4caf50;">
                    <div style="padding: 16px;">
                        <h4 style="color: #4caf50; margin: 0 0 12px; font-size: 14px; display: flex; align-items: center;">
                            <i class="fas fa-info-circle" style="margin-right: 8px;"></i>
                            路径配置说明
                        </h4>
                        <div style="font-size: 13px; color: #b8e6b8; line-height: 1.5;">
                            <p style="margin: 0 0 8px;"><strong>文档路径：</strong>存储物料相关的技术文档、图纸、规格书等文件</p>
                            <p style="margin: 0 0 8px;"><strong>图片路径：</strong>存储检验记录中上传的照片、检验图片等</p>
                            <p style="margin: 0;"><strong>建议：</strong>使用不同的文件夹分类存储，便于备份和管理</p>
                        </div>
                    </div>
                </div>

                <div class="settings-group">
                    <div class="setting-item">
                        <div class="setting-info">
                            <h3 class="setting-title">文档文件夹路径</h3>
                            <p class="setting-description">设置物料相关文档的存储路径。系统将在此路径下按物料编码创建子文件夹来存储相关图纸和文档。</p>
                        </div>
                        <div class="setting-control">
                            <div class="path-browser">
                                <input type="text"
                                       id="document_path"
                                       name="document_path"
                                       class="form-input"
                                       value="{{ settings.get('document_path', 'D:/Documents/Materials') }}"
                                       placeholder="请输入文档文件夹的完整路径">
                                <button type="button" class="browse-btn" onclick="browsePath(document.getElementById('document_path'))">
                                    <i class="fas fa-folder-open"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="setting-item">
                        <div class="setting-info">
                            <h3 class="setting-title">图片上传路径</h3>
                            <p class="setting-description">设置检验记录中图片文件的上传存储路径。支持相对路径和绝对路径。建议使用绝对路径以确保文件安全存储。</p>
                        </div>
                        <div class="setting-control">
                            <div class="path-browser">
                                <input type="text"
                                       id="upload_path"
                                       name="upload_path"
                                       class="form-input"
                                       value="{{ settings.get('upload_path', 'static/uploads') }}"
                                       placeholder="请输入图片上传路径">
                                <button type="button" class="browse-btn" onclick="browsePath(document.getElementById('upload_path'))">
                                    <i class="fas fa-folder-open"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="setting-item">
                        <div class="setting-info">
                            <h3 class="setting-title">路径自动创建</h3>
                            <p class="setting-description">当指定的路径不存在时，系统自动创建相应的文件夹</p>
                        </div>
                        <div class="setting-control">
                            <div class="toggle-switch active" onclick="toggleSwitch(this)"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统参数设置 -->
            <div id="system-tab" class="tab-content">
                <div class="content-header">
                    <h1 class="content-title">系统参数</h1>
                    <p class="content-subtitle">配置系统运行的相关参数</p>
                </div>

                <div class="settings-group">
                    <div class="setting-item">
                        <div class="setting-info">
                            <h3 class="setting-title">最大文件大小</h3>
                            <p class="setting-description">设置单个图片文件的最大上传大小限制（1-50MB）</p>
                        </div>
                        <div class="setting-control">
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <input type="number"
                                       id="max_file_size"
                                       name="max_file_size"
                                       class="form-input"
                                       style="width: 80px;"
                                       value="{{ settings.get('max_file_size', '5') }}"
                                       min="1"
                                       max="50"
                                       placeholder="5">
                                <span style="color: #888; font-size: 13px;">MB</span>
                            </div>
                        </div>
                    </div>

                    <div class="setting-item">
                        <div class="setting-info">
                            <h3 class="setting-title">图片自动压缩</h3>
                            <p class="setting-description">自动压缩上传的图片以节省存储空间，保持图片质量的同时减小文件大小</p>
                        </div>
                        <div class="setting-control">
                            <div class="toggle-switch active" onclick="toggleSwitch(this)"></div>
                        </div>
                    </div>

                    <div class="setting-item">
                        <div class="setting-info">
                            <h3 class="setting-title">允许的图片格式</h3>
                            <p class="setting-description">设置允许上传的图片文件格式</p>
                        </div>
                        <div class="setting-control">
                            <div style="display: flex; flex-wrap: wrap; gap: 8px;">
                                <label style="display: flex; align-items: center; gap: 4px; font-size: 13px;">
                                    <input type="checkbox" checked style="margin: 0;"> JPG/JPEG
                                </label>
                                <label style="display: flex; align-items: center; gap: 4px; font-size: 13px;">
                                    <input type="checkbox" checked style="margin: 0;"> PNG
                                </label>
                                <label style="display: flex; align-items: center; gap: 4px; font-size: 13px;">
                                    <input type="checkbox" style="margin: 0;"> GIF
                                </label>
                                <label style="display: flex; align-items: center; gap: 4px; font-size: 13px;">
                                    <input type="checkbox" style="margin: 0;"> BMP
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="setting-item">
                        <div class="setting-info">
                            <h3 class="setting-title">数据备份频率</h3>
                            <p class="setting-description">设置系统自动备份数据的频率</p>
                        </div>
                        <div class="setting-control">
                            <select class="form-input" style="width: 150px;">
                                <option value="daily">每日备份</option>
                                <option value="weekly" selected>每周备份</option>
                                <option value="monthly">每月备份</option>
                                <option value="manual">手动备份</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 安全设置 -->
            <div id="security-tab" class="tab-content">
                <div class="content-header">
                    <h1 class="content-title">安全设置</h1>
                    <p class="content-subtitle">配置系统安全相关参数</p>
                </div>

                <div class="settings-group">
                    <div class="setting-item">
                        <div class="setting-info">
                            <h3 class="setting-title">会话超时</h3>
                            <p class="setting-description">用户无操作后自动退出的时间（分钟）</p>
                        </div>
                        <div class="setting-control">
                            <input type="number"
                                   class="form-input"
                                   value="60"
                                   min="5"
                                   max="480"
                                   placeholder="60">
                        </div>
                    </div>

                    <div class="setting-item">
                        <div class="setting-info">
                            <h3 class="setting-title">操作日志</h3>
                            <p class="setting-description">记录用户的重要操作日志</p>
                        </div>
                        <div class="setting-control">
                            <div class="toggle-switch active" onclick="toggleSwitch(this)"></div>
                        </div>
                    </div>

                    <div class="setting-item">
                        <div class="setting-info">
                            <h3 class="setting-title">数据加密</h3>
                            <p class="setting-description">对敏感数据进行加密存储</p>
                        </div>
                        <div class="setting-control">
                            <div class="toggle-switch" onclick="toggleSwitch(this)"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="btn-group">
                <button type="button" class="btn btn-secondary" onclick="resetForm()">
                    <i class="fas fa-undo"></i> 重置
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> 保存设置
                </button>
            </div>
        </form>
    </div>
</div>

<script src="{{ url_for('static', filename='js/folder-browser.js') }}"></script>



{% endblock %}
