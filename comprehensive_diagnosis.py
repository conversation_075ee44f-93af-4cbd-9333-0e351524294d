#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面诊断脚本 - 检查物料信息自动获取功能的所有可能问题
"""

import sys
import os
import requests
import json
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from db_config import get_db_connection

def print_section(title):
    """打印分节标题"""
    print("\n" + "="*60)
    print(f"🔍 {title}")
    print("="*60)

def print_subsection(title):
    """打印子节标题"""
    print(f"\n📋 {title}")
    print("-"*40)

def test_database_connection():
    """测试数据库连接"""
    print_section("数据库连接测试")
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # 测试连接
        cursor.execute("SELECT 1 as test")
        result = cursor.fetchone()
        print("✅ 数据库连接成功")
        
        # 检查materials表
        cursor.execute("SHOW TABLES LIKE 'materials'")
        table_exists = cursor.fetchone()
        if table_exists:
            print("✅ materials表存在")
            
            # 检查表结构
            cursor.execute("DESCRIBE materials")
            columns = cursor.fetchall()
            print("📋 materials表结构:")
            for col in columns:
                print(f"  - {col['Field']} ({col['Type']})")
            
            # 检查数据数量
            cursor.execute("SELECT COUNT(*) as count FROM materials")
            count_result = cursor.fetchone()
            print(f"📊 materials表记录数: {count_result['count']}")
            
            if count_result['count'] > 0:
                # 显示前几条数据
                cursor.execute("SELECT * FROM materials LIMIT 3")
                materials = cursor.fetchall()
                print("📋 前3条物料数据:")
                for material in materials:
                    print(f"  - {material['material_number']}: {material['material_name']} ({material.get('specification', 'N/A')})")
                return materials[0]['material_number']  # 返回第一个料号用于测试
            else:
                print("❌ materials表中没有数据")
                return None
        else:
            print("❌ materials表不存在")
            return None
            
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return None
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def test_flask_server():
    """测试Flask服务器是否运行"""
    print_section("Flask服务器测试")
    try:
        response = requests.get("http://localhost:5000", timeout=5)
        print(f"✅ Flask服务器运行正常 (状态码: {response.status_code})")
        return True
    except requests.exceptions.ConnectionError:
        print("❌ Flask服务器未运行或无法连接")
        print("请确保运行了: python app.py")
        return False
    except Exception as e:
        print(f"❌ Flask服务器测试失败: {e}")
        return False

def test_material_api(material_number):
    """测试物料信息API"""
    print_section("物料信息API测试")
    if not material_number:
        print("❌ 没有可用的测试料号")
        return False
    
    try:
        url = f"http://localhost:5000/api/material_info/{material_number}"
        print(f"🌐 请求URL: {url}")
        
        response = requests.get(url, timeout=10)
        print(f"📡 响应状态码: {response.status_code}")
        print(f"📡 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print("✅ API调用成功")
                print("📋 返回数据:")
                print(json.dumps(data, indent=2, ensure_ascii=False))
                return True
            except json.JSONDecodeError:
                print("❌ 响应不是有效的JSON格式")
                print(f"响应内容: {response.text}")
                return False
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def test_supplier_api(material_number):
    """测试供应商信息API"""
    print_section("供应商信息API测试")
    if not material_number:
        print("❌ 没有可用的测试料号")
        return False
    
    try:
        url = f"http://localhost:5000/incoming/api/recent_supplier/{material_number}"
        print(f"🌐 请求URL: {url}")
        
        response = requests.get(url, timeout=10)
        print(f"📡 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print("✅ 供应商API调用成功")
                print("📋 返回数据:")
                print(json.dumps(data, indent=2, ensure_ascii=False))
                return True
            except json.JSONDecodeError:
                print("❌ 响应不是有效的JSON格式")
                print(f"响应内容: {response.text}")
                return False
        else:
            print(f"❌ 供应商API调用失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 供应商API测试失败: {e}")
        return False

def test_batch_import_page():
    """测试批量导入页面是否可访问"""
    print_section("批量导入页面测试")
    try:
        url = "http://localhost:5000/incoming_inspection/batch_import/sampling"
        print(f"🌐 请求URL: {url}")
        
        response = requests.get(url, timeout=10)
        print(f"📡 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 批量导入页面可访问")
            
            # 检查页面内容
            content = response.text
            if 'fetchMaterialInfo' in content:
                print("✅ 页面包含fetchMaterialInfo函数")
            else:
                print("❌ 页面不包含fetchMaterialInfo函数")
            
            if 'bindMaterialCodeEvents' in content:
                print("✅ 页面包含bindMaterialCodeEvents函数")
            else:
                print("❌ 页面不包含bindMaterialCodeEvents函数")
            
            if 'DOMContentLoaded' in content:
                print("✅ 页面包含DOMContentLoaded事件监听")
            else:
                print("❌ 页面不包含DOMContentLoaded事件监听")
            
            return True
        else:
            print(f"❌ 批量导入页面访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 页面测试失败: {e}")
        return False

def check_javascript_syntax():
    """检查JavaScript语法问题"""
    print_section("JavaScript语法检查")
    
    # 读取模板文件
    template_path = "blueprints/incoming_inspection/templates/batch_import_sampling.html"
    if not os.path.exists(template_path):
        print(f"❌ 模板文件不存在: {template_path}")
        return False
    
    try:
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查常见的JavaScript问题
        issues = []
        
        # 检查重复声明
        if content.count('let previewData') > 1:
            issues.append("重复声明previewData变量")
        
        # 检查函数定义
        if 'function fetchMaterialInfo' not in content:
            issues.append("缺少fetchMaterialInfo函数定义")
        
        if 'function bindMaterialCodeEvents' not in content:
            issues.append("缺少bindMaterialCodeEvents函数定义")
        
        # 检查事件绑定
        if 'addEventListener' not in content:
            issues.append("缺少addEventListener事件绑定")
        
        # 检查API调用
        if '/api/material_info/' not in content:
            issues.append("缺少物料信息API调用")
        
        if '/incoming/api/recent_supplier/' not in content:
            issues.append("缺少供应商信息API调用")
        
        if issues:
            print("❌ 发现JavaScript问题:")
            for issue in issues:
                print(f"  - {issue}")
            return False
        else:
            print("✅ JavaScript语法检查通过")
            return True
            
    except Exception as e:
        print(f"❌ JavaScript语法检查失败: {e}")
        return False

def generate_test_data():
    """生成测试数据"""
    print_section("生成测试数据")
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查是否已有数据
        cursor.execute("SELECT COUNT(*) FROM materials")
        count = cursor.fetchone()[0]
        
        if count > 0:
            print(f"✅ materials表已有 {count} 条数据")
            return True
        
        # 插入测试数据
        test_materials = [
            ('TEST001', '测试物料1', '100x50x2mm', '铝合金', '银色'),
            ('TEST002', '测试物料2', '200x100x5mm', '不锈钢', '银色'),
            ('TEST003', '测试物料3', '50x25x1mm', '铜合金', '黄色'),
        ]
        
        for material in test_materials:
            cursor.execute("""
                INSERT IGNORE INTO materials (material_number, material_name, specification, material_type, color)
                VALUES (%s, %s, %s, %s, %s)
            """, material)
        
        conn.commit()
        print(f"✅ 成功插入 {len(test_materials)} 条测试数据")
        return True
        
    except Exception as e:
        print(f"❌ 生成测试数据失败: {e}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def main():
    """主函数"""
    print("🧪 物料信息自动获取功能全面诊断")
    print("="*60)
    
    # 1. 测试数据库连接
    test_material = test_database_connection()
    
    # 2. 如果没有数据，生成测试数据
    if not test_material:
        if generate_test_data():
            test_material = test_database_connection()
    
    # 3. 测试Flask服务器
    server_ok = test_flask_server()
    
    if not server_ok:
        print("\n❌ Flask服务器未运行，无法进行API测试")
        print("请先运行: python app.py")
        return
    
    # 4. 测试批量导入页面
    test_batch_import_page()
    
    # 5. 检查JavaScript语法
    check_javascript_syntax()
    
    # 6. 测试API
    if test_material:
        test_material_api(test_material)
        test_supplier_api(test_material)
    
    print_section("诊断总结")
    print("如果所有测试都通过，但仍然无法触发自动获取，请：")
    print("1. 打开浏览器开发者工具 (F12)")
    print("2. 切换到Console标签")
    print("3. 访问批量导入页面")
    print("4. 在料号输入框输入料号并失去焦点")
    print("5. 查看控制台输出的调试信息")
    print("6. 截图控制台信息并提供给开发者")

if __name__ == "__main__":
    main()
