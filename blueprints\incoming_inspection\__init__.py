from flask import Blueprint

# 主来料检验蓝图
incoming_inspection_bp = Blueprint('incoming_inspection', __name__, 
                                 template_folder='templates',
                                 url_prefix='/incoming')

# 全检蓝图 - 保持原有URL路径
full_inspection_bp = Blueprint('full_inspection', __name__, 
                             template_folder='templates',
                             url_prefix='/full_inspection')

# 抽检蓝图 - 保持原有URL路径
sampling_inspection_bp = Blueprint('sampling_inspection', __name__, 
                                 template_folder='templates',
                                 url_prefix='/sampling_inspection')

from . import routes
from . import api 