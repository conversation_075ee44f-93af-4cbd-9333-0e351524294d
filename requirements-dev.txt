# ===================================
# 开发环境依赖包
# ===================================

# 包含生产环境依赖
-r requirements.txt

# 代码格式化和检查
black==23.9.1
flake8==6.1.0
isort==5.12.0

# 测试框架
pytest==7.4.3
pytest-flask==1.3.0
pytest-cov==4.1.0

# 开发工具
ipython==8.17.2
jupyter==1.0.0

# 调试工具
flask-debugtoolbar==0.13.1

# 文档生成
sphinx==7.2.6

# 类型检查
mypy==1.7.1

# 安全检查
bandit==1.7.5
safety==2.3.5

# 性能分析
memory-profiler==0.61.0
line-profiler==4.1.1

# 数据库迁移（如果需要）
alembic==1.12.1

# API文档
flask-restx==1.3.0

# 环境管理
python-decouple==3.8
