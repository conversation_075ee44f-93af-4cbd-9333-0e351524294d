#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为materials表添加缺失的字段
添加material_category和inspection_type字段
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import Config
import mysql.connector

def get_db_connection():
    """建立与指定数据库的连接"""
    config = Config()
    return mysql.connector.connect(
        host=config.DB_HOST,
        user=config.DB_USER,
        password=config.DB_PASSWORD,
        database=config.DB_NAME
    )

def add_material_fields():
    """为materials表添加缺失的字段"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        print("开始为materials表添加缺失字段...")
        
        # 检查字段是否已存在
        cursor.execute("DESCRIBE materials")
        existing_columns = [column[0] for column in cursor.fetchall()]
        
        # 添加material_category字段
        if 'material_category' not in existing_columns:
            cursor.execute("""
                ALTER TABLE materials 
                ADD COLUMN material_category VARCHAR(50) DEFAULT NULL 
                COMMENT '物料类型：原材料、半成品、成品、辅料、包装材料、工具、其他'
            """)
            print("✅ 添加material_category字段成功")
        else:
            print("⚠️  material_category字段已存在")
        
        # 添加inspection_type字段
        if 'inspection_type' not in existing_columns:
            cursor.execute("""
                ALTER TABLE materials 
                ADD COLUMN inspection_type VARCHAR(20) DEFAULT NULL 
                COMMENT '检验类型：抽样、全检、免检'
            """)
            print("✅ 添加inspection_type字段成功")
        else:
            print("⚠️  inspection_type字段已存在")
        
        # 提交更改
        conn.commit()
        
        # 验证字段添加
        cursor.execute("DESCRIBE materials")
        columns = cursor.fetchall()
        print("\n📋 当前materials表结构:")
        for column in columns:
            print(f"  - {column[0]} ({column[1]})")
        
        print("\n🎉 字段添加完成！")
        return True
        
    except Exception as e:
        print(f"❌ 添加字段失败: {e}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def update_sample_data():
    """为现有的示例数据添加物料类型和检验类型"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        print("\n开始更新示例数据...")
        
        # 定义物料类型和检验类型的映射
        material_updates = [
            # 料号前缀为ABC的设为原材料
            ("UPDATE materials SET material_category = '原材料', inspection_type = '抽样' WHERE material_number LIKE 'ABC%'", "ABC系列 -> 原材料/抽样"),
            # 料号前缀为DEF的设为半成品
            ("UPDATE materials SET material_category = '半成品', inspection_type = '全检' WHERE material_number LIKE 'DEF%'", "DEF系列 -> 半成品/全检"),
            # 料号前缀为GHI的设为成品
            ("UPDATE materials SET material_category = '成品', inspection_type = '全检' WHERE material_number LIKE 'GHI%'", "GHI系列 -> 成品/全检"),
            # 料号前缀为JKL的设为辅料
            ("UPDATE materials SET material_category = '辅料', inspection_type = '抽样' WHERE material_number LIKE 'JKL%'", "JKL系列 -> 辅料/抽样"),
            # 料号前缀为MNO的设为包装材料
            ("UPDATE materials SET material_category = '包装材料', inspection_type = '免检' WHERE material_number LIKE 'MNO%'", "MNO系列 -> 包装材料/免检"),
        ]
        
        for update_sql, description in material_updates:
            cursor.execute(update_sql)
            affected_rows = cursor.rowcount
            if affected_rows > 0:
                print(f"✅ {description} - 更新了 {affected_rows} 条记录")
        
        # 为其他没有分类的物料设置默认值
        cursor.execute("""
            UPDATE materials 
            SET material_category = '其他', inspection_type = '抽样' 
            WHERE material_category IS NULL OR inspection_type IS NULL
        """)
        affected_rows = cursor.rowcount
        if affected_rows > 0:
            print(f"✅ 其他物料 -> 其他/抽样 - 更新了 {affected_rows} 条记录")
        
        conn.commit()
        
        # 验证更新结果
        cursor.execute("""
            SELECT material_category, inspection_type, COUNT(*) as count 
            FROM materials 
            GROUP BY material_category, inspection_type 
            ORDER BY material_category, inspection_type
        """)
        results = cursor.fetchall()
        
        print("\n📊 更新后的数据分布:")
        for result in results:
            print(f"  - {result[0]} / {result[1]}: {result[2]} 条")
        
        print("\n🎉 示例数据更新完成！")
        return True
        
    except Exception as e:
        print(f"❌ 更新示例数据失败: {e}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def main():
    """主函数"""
    print("🚀 开始为materials表添加缺失字段")
    print("=" * 50)
    
    # 1. 添加字段
    if not add_material_fields():
        print("❌ 字段添加失败，程序退出")
        return False
    
    # 2. 更新示例数据
    if not update_sample_data():
        print("❌ 示例数据更新失败，程序退出")
        return False
    
    print("=" * 50)
    print("🎉 materials表字段添加和数据更新完成！")
    print("\n现在可以使用高级搜索功能了：")
    print("  - 物料类型筛选")
    print("  - 检验类型筛选")
    
    return True

if __name__ == "__main__":
    main()
