#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
报告编码生成工具
"""

import datetime
from db_config import get_db_connection

def generate_report_code(inspection_type, inspection_date):
    """生成新的报告编码
    
    Args:
        inspection_type: 'sampling' 或 'full'
        inspection_date: 检验日期 (datetime 或 string)
    
    Returns:
        str: 报告编码，格式如 IQC-JL-202507200001 或 IQC-QJ-202507200001
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 处理日期
        if isinstance(inspection_date, str):
            date_obj = datetime.datetime.strptime(inspection_date, '%Y-%m-%d')
        else:
            date_obj = inspection_date
        
        date_str = date_obj.strftime('%Y%m%d')
        
        # 确定前缀和表名
        if inspection_type == 'sampling':
            prefix = f"IQC-JL-{date_str}"
            table_name = "sampling_inspection"
        else:  # full
            prefix = f"IQC-QJ-{date_str}"
            table_name = "full_inspection"
        
        # 查询当天已有的最大序号
        cursor.execute(f"""
            SELECT report_code FROM {table_name} 
            WHERE report_code LIKE %s 
            ORDER BY report_code DESC 
            LIMIT 1
        """, (f"{prefix}%",))
        
        result = cursor.fetchone()
        
        if result:
            # 提取序号并加1
            last_code = result[0]
            try:
                last_seq = int(last_code[-4:])
                new_seq = last_seq + 1
            except (ValueError, IndexError):
                new_seq = 1
        else:
            # 当天第一条记录
            new_seq = 1
        
        report_code = f"{prefix}{new_seq:04d}"
        
        cursor.close()
        conn.close()
        
        return report_code
        
    except Exception as e:
        print(f"生成报告编码失败: {e}")
        # 返回一个基于时间戳的备用编码
        timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
        prefix = "IQC-JL" if inspection_type == 'sampling' else "IQC-QJ"
        return f"{prefix}-{timestamp}"

def validate_report_code(report_code):
    """验证报告编码格式是否正确
    
    Args:
        report_code: 报告编码
    
    Returns:
        bool: 是否有效
    """
    if not report_code:
        return False
    
    # 检查格式：IQC-JL-YYYYMMDDNNNN 或 IQC-QJ-YYYYMMDDNNNN
    import re
    pattern = r'^IQC-(JL|QJ)-\d{8}\d{4}$'
    return bool(re.match(pattern, report_code))

def get_report_code_info(report_code):
    """解析报告编码信息
    
    Args:
        report_code: 报告编码
    
    Returns:
        dict: 包含类型、日期、序号的信息
    """
    if not validate_report_code(report_code):
        return None
    
    parts = report_code.split('-')
    if len(parts) != 3:
        return None
    
    inspection_type = 'sampling' if parts[1] == 'JL' else 'full'
    date_and_seq = parts[2]
    
    try:
        date_str = date_and_seq[:8]
        seq_str = date_and_seq[8:]
        
        date_obj = datetime.datetime.strptime(date_str, '%Y%m%d')
        sequence = int(seq_str)
        
        return {
            'type': inspection_type,
            'type_name': '抽样检验' if inspection_type == 'sampling' else '全部检验',
            'date': date_obj,
            'date_str': date_obj.strftime('%Y-%m-%d'),
            'sequence': sequence
        }
    except (ValueError, IndexError):
        return None

def check_report_code_exists(report_code, inspection_type):
    """检查报告编码是否已存在
    
    Args:
        report_code: 报告编码
        inspection_type: 检验类型
    
    Returns:
        bool: 是否存在
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        table_name = "sampling_inspection" if inspection_type == 'sampling' else "full_inspection"
        
        cursor.execute(f"""
            SELECT COUNT(*) FROM {table_name} 
            WHERE report_code = %s
        """, (report_code,))
        
        count = cursor.fetchone()[0]
        
        cursor.close()
        conn.close()
        
        return count > 0
        
    except Exception as e:
        print(f"检查报告编码失败: {e}")
        return False

if __name__ == "__main__":
    # 测试报告编码生成
    print("🧪 测试报告编码生成器")
    print("=" * 40)
    
    # 测试生成
    test_date = datetime.datetime.now()
    sampling_code = generate_report_code('sampling', test_date)
    full_code = generate_report_code('full', test_date)
    
    print(f"抽样检验报告编码: {sampling_code}")
    print(f"全部检验报告编码: {full_code}")
    
    # 测试验证
    print(f"\n编码格式验证:")
    print(f"  {sampling_code}: {validate_report_code(sampling_code)}")
    print(f"  {full_code}: {validate_report_code(full_code)}")
    print(f"  'IQC-JL-20250720': {validate_report_code('IQC-JL-20250720')}")
    print(f"  'INVALID-CODE': {validate_report_code('INVALID-CODE')}")
    
    # 测试解析
    print(f"\n编码信息解析:")
    info = get_report_code_info(sampling_code)
    if info:
        print(f"  类型: {info['type_name']}")
        print(f"  日期: {info['date_str']}")
        print(f"  序号: {info['sequence']}")
    
    print("\n✅ 测试完成")
