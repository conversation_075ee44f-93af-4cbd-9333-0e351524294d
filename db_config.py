#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库配置和连接管理
提供数据库连接和基本操作功能
"""

import mysql.connector
import sqlite3
import os
from config import Config

def get_db_connection():
    """建立与MySQL数据库的连接"""
    config = Config()
    return mysql.connector.connect(
        host=config.DB_HOST,
        user=config.DB_USER,
        password=config.DB_PASSWORD,
        database=config.DB_NAME
    )

def get_mysql_connection():
    """建立与MySQL服务器的连接（不指定数据库）"""
    config = Config()
    return mysql.connector.connect(
        host=config.DB_HOST,
        user=config.DB_USER,
        password=config.DB_PASSWORD
    )

def test_connection():
    """测试数据库连接"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        cursor.close()
        conn.close()
        return True, "数据库连接成功"
    except Exception as e:
        return False, f"数据库连接失败: {e}"

def get_table_info():
    """获取数据库表信息"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取所有表名
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        
        table_info = {}
        for table in tables:
            table_name = table[0]
            
            # 获取表的记录数
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            
            table_info[table_name] = count
        
        cursor.close()
        conn.close()
        return table_info
        
    except Exception as e:
        print(f"获取表信息失败: {e}")
        return {}

if __name__ == "__main__":
    # 测试数据库连接
    success, message = test_connection()
    print(f"连接测试: {message}")
    
    if success:
        # 显示表信息
        tables = get_table_info()
        if tables:
            print("\n数据库表信息:")
            for table_name, count in tables.items():
                print(f"  {table_name}: {count} 条记录")
        else:
            print("未找到数据表或获取信息失败")
