from flask import render_template, request, jsonify, current_app
from . import process_control_bp
from db_config import get_db_connection
import os
from werkzeug.utils import secure_filename
import json
from datetime import datetime

# 车间异常记录页面
@process_control_bp.route('/workshop_abnormal')
def workshop_abnormal():
    # 获取分页参数
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    
    # 获取筛选参数
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')
    product_number = request.args.get('product_number', '')
    workshop = request.args.get('workshop', '')
    abnormal_type = request.args.get('abnormal_type', '')
    
    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)
    
    try:
        # 构建查询条件
        conditions = []
        params = []
        
        if start_date:
            conditions.append("record_date >= %s")
            params.append(start_date)
        
        if end_date:
            conditions.append("record_date <= %s")
            params.append(end_date)
        
        if product_number:
            conditions.append("product_number LIKE %s")
            params.append(f"%{product_number}%")
        
        if workshop:
            conditions.append("workshop LIKE %s")
            params.append(f"%{workshop}%")
            
        if abnormal_type:
            conditions.append("abnormal_type LIKE %s")
            params.append(f"%{abnormal_type}%")
        
        # 组合查询条件
        where_clause = " AND ".join(conditions)
        if where_clause:
            where_clause = "WHERE " + where_clause
        
        # 计算总记录数
        count_query = f"SELECT COUNT(*) as total FROM workshop_abnormal {where_clause}"
        cursor.execute(count_query, params)
        total_records = cursor.fetchone()['total']
        
        # 计算总页数
        total_pages = (total_records + per_page - 1) // per_page
        
        # 确保页码在有效范围内
        if page < 1:
            page = 1
        elif page > total_pages and total_pages > 0:
            page = total_pages
        
        # 计算偏移量
        offset = (page - 1) * per_page
        
        # 获取分页后的记录
        query = f"""
            SELECT * FROM workshop_abnormal 
            {where_clause}
            ORDER BY record_date DESC
            LIMIT %s OFFSET %s
        """
        cursor.execute(query, params + [per_page, offset])
        records = cursor.fetchall()
        
        # 获取所有车间列表（用于筛选）
        cursor.execute("SELECT DISTINCT workshop FROM workshop_abnormal ORDER BY workshop")
        workshops = [row['workshop'] for row in cursor.fetchall()]
        
        # 获取所有异常类型列表（用于筛选）
        cursor.execute("SELECT DISTINCT abnormal_type FROM workshop_abnormal ORDER BY abnormal_type")
        abnormal_types = [row['abnormal_type'] for row in cursor.fetchall()]
        
        # 渲染模板，传递分页和筛选信息
        return render_template(
            'process_control/workshop_abnormal.html', 
            records=records,
            workshops=workshops,
            abnormal_types=abnormal_types,
            page=page,
            per_page=per_page,
            total_pages=total_pages,
            total_records=total_records,
            start_date=start_date,
            end_date=end_date,
            product_number=product_number,
            workshop=workshop,
            abnormal_type=abnormal_type
        )
    except Exception as e:
        return jsonify({"error": str(e)}), 500
    finally:
        cursor.close()
        conn.close()

# 新增异常记录页面
@process_control_bp.route('/new_abnormal')
def new_abnormal():
    return render_template('process_control/new_abnormal.html')

# 添加异常记录API
@process_control_bp.route('/api/add_abnormal', methods=['POST'])
def add_abnormal():
    try:
        # 获取表单数据
        data = request.form.to_dict()
        
        # 处理上传的图片
        image_path = None
        if 'image' in request.files:
            image = request.files['image']
            if image.filename != '':
                filename = secure_filename(image.filename)
                # 生成唯一文件名
                unique_filename = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{filename}"
                # 保存文件
                image_path = os.path.join('static', 'uploads', unique_filename)
                image.save(os.path.join(current_app.config['UPLOAD_FOLDER'], unique_filename))
        
        # 处理异常详情（JSON格式）
        abnormal_details = request.form.get('abnormal_details', '[]')
        
        # 连接数据库
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 插入记录
        query = """
            INSERT INTO workshop_abnormal (
                product_number, product_name, workshop, process_name,
                abnormal_type, abnormal_details, record_date, recorder,
                status, image_path, remarks
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        cursor.execute(query, (
            data.get('product_number', ''),
            data.get('product_name', ''),
            data.get('workshop', ''),
            data.get('process_name', ''),
            data.get('abnormal_type', ''),
            abnormal_details,
            data.get('record_date', datetime.now().strftime('%Y-%m-%d')),
            data.get('recorder', ''),
            data.get('status', '待处理'),
            image_path,
            data.get('remarks', '')
        ))
        
        conn.commit()
        record_id = cursor.lastrowid
        
        cursor.close()
        conn.close()
        
        return jsonify({"success": True, "message": "异常记录添加成功", "record_id": record_id})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500 