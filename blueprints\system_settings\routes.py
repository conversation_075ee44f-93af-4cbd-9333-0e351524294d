from flask import render_template, request, jsonify, flash, redirect, url_for
from . import system_settings_bp
from db_config import get_db_connection
import os

@system_settings_bp.route('/')
def index():
    """系统设置首页"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # 获取当前设置
        cursor.execute("SELECT * FROM system_settings")
        settings = cursor.fetchall()
        
        # 转换为字典格式
        settings_dict = {}
        for setting in settings:
            settings_dict[setting['setting_key']] = setting['setting_value']
        
        return render_template('system_settings/index.html', settings=settings_dict)
        
    except Exception as e:
        flash(f'获取系统设置失败: {str(e)}', 'error')
        return render_template('system_settings/index.html', settings={})
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

@system_settings_bp.route('/save', methods=['POST'])
def save_settings():
    """保存系统设置"""
    try:
        # 获取表单数据
        document_path = request.form.get('document_path', '').strip()
        upload_path = request.form.get('upload_path', '').strip()
        max_file_size = request.form.get('max_file_size', '5').strip()
        
        # 验证路径
        if document_path:
            # 尝试创建目录（如果不存在）
            try:
                if not os.path.exists(document_path):
                    os.makedirs(document_path, exist_ok=True)
                    flash(f'已创建文档路径: {document_path}', 'success')

                # 测试路径是否可写
                test_file = os.path.join(document_path, 'test_write.tmp')
                try:
                    with open(test_file, 'w') as f:
                        f.write('test')
                    os.remove(test_file)
                except Exception:
                    flash('指定的文档路径无法写入，请检查权限设置', 'error')
                    return redirect(url_for('system_settings.index'))

            except Exception as e:
                flash(f'无法创建或访问文档路径: {str(e)}', 'error')
                return redirect(url_for('system_settings.index'))
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 更新或插入设置
        settings_to_save = [
            ('document_path', document_path, '文档文件夹路径'),
            ('upload_path', upload_path, '图片上传路径'),
            ('max_file_size', max_file_size, '最大文件大小(MB)')
        ]

        for setting_key, setting_value, description in settings_to_save:
            if setting_value:  # 只保存非空值
                cursor.execute("""
                    INSERT INTO system_settings (setting_key, setting_value, description)
                    VALUES (%s, %s, %s)
                    ON DUPLICATE KEY UPDATE
                    setting_value = VALUES(setting_value),
                    updated_at = CURRENT_TIMESTAMP
                """, (setting_key, setting_value, description))
        
        conn.commit()
        flash('系统设置保存成功', 'success')
        
    except Exception as e:
        flash(f'保存系统设置失败: {str(e)}', 'error')
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()
    
    return redirect(url_for('system_settings.index'))

@system_settings_bp.route('/download_settings')
def download_settings():
    """文件下载设置页面"""
    return render_template('download_settings.html')
