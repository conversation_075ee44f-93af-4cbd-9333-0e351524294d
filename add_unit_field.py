#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为materials表添加unit字段
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from db_config import get_db_connection

def add_unit_field():
    """为materials表添加unit字段"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        print("🔧 开始为materials表添加unit字段...")
        
        # 检查字段是否已存在
        cursor.execute("DESCRIBE materials")
        existing_columns = [column[0] for column in cursor.fetchall()]
        
        if 'unit' not in existing_columns:
            # 添加unit字段
            cursor.execute("""
                ALTER TABLE materials 
                ADD COLUMN unit VARCHAR(20) DEFAULT NULL 
                COMMENT '单位：个、kg、m、L等'
            """)
            print("✅ 添加unit字段成功")
        else:
            print("⚠️ unit字段已存在")
        
        # 提交更改
        conn.commit()
        
        # 验证字段添加
        cursor.execute("DESCRIBE materials")
        columns = cursor.fetchall()
        print("\n📋 当前materials表结构:")
        for column in columns:
            print(f"  - {column[0]} ({column[1]})")
        
        return True
        
    except Exception as e:
        print(f"❌ 添加字段失败: {e}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def update_sample_data():
    """为现有的示例数据添加单位信息"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        print("\n🔧 开始更新示例数据的单位信息...")
        
        # 定义不同类型物料的默认单位
        unit_updates = [
            # 电子元件类
            ("UPDATE materials SET unit = '个' WHERE material_number LIKE 'ABC%' OR material_name LIKE '%电阻%' OR material_name LIKE '%电容%'", "电子元件 -> 个"),
            # 金属材料类
            ("UPDATE materials SET unit = 'kg' WHERE material_name LIKE '%铝%' OR material_name LIKE '%钢%' OR material_name LIKE '%铜%'", "金属材料 -> kg"),
            # 板材类
            ("UPDATE materials SET unit = '张' WHERE material_name LIKE '%板%' OR specification LIKE '%mm'", "板材类 -> 张"),
            # 线材类
            ("UPDATE materials SET unit = 'm' WHERE material_name LIKE '%线%' OR material_name LIKE '%缆%'", "线材类 -> m"),
            # 液体类
            ("UPDATE materials SET unit = 'L' WHERE material_name LIKE '%油%' OR material_name LIKE '%液%'", "液体类 -> L"),
            # 测试物料
            ("UPDATE materials SET unit = '个' WHERE material_number LIKE 'TEST%'", "测试物料 -> 个"),
        ]
        
        for update_sql, description in unit_updates:
            cursor.execute(update_sql)
            affected_rows = cursor.rowcount
            if affected_rows > 0:
                print(f"✅ {description} - 更新了 {affected_rows} 条记录")
        
        # 为其他没有单位的物料设置默认值
        cursor.execute("""
            UPDATE materials 
            SET unit = '个' 
            WHERE unit IS NULL OR unit = ''
        """)
        affected_rows = cursor.rowcount
        if affected_rows > 0:
            print(f"✅ 其他物料 -> 个 - 更新了 {affected_rows} 条记录")
        
        # 提交更改
        conn.commit()
        
        # 显示更新后的数据
        cursor.execute("SELECT material_number, material_name, unit FROM materials LIMIT 10")
        materials = cursor.fetchall()
        print("\n📋 前10条物料的单位信息:")
        for material in materials:
            print(f"  - {material[0]}: {material[1]} ({material[2]})")
        
        return True
        
    except Exception as e:
        print(f"❌ 更新示例数据失败: {e}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def main():
    """主函数"""
    print("🚀 开始为materials表添加unit字段")
    print("=" * 50)
    
    # 1. 添加字段
    if not add_unit_field():
        print("❌ 字段添加失败，程序退出")
        return False
    
    # 2. 更新示例数据
    if not update_sample_data():
        print("❌ 示例数据更新失败，程序退出")
        return False
    
    print("=" * 50)
    print("🎉 materials表unit字段添加和数据更新完成！")
    print("\n现在物料信息API会返回单位信息：")
    print("  - 电子元件：个")
    print("  - 金属材料：kg")
    print("  - 板材类：张")
    print("  - 线材类：m")
    print("  - 液体类：L")
    
    return True

if __name__ == "__main__":
    main()
