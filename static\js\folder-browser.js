// 文件夹浏览器功能
let currentBrowseTarget = null;
let currentPath = '';
let pathHistory = [];

// 标签页切换功能
document.addEventListener('DOMContentLoaded', function() {
    const navLinks = document.querySelectorAll('.nav-link');
    const tabContents = document.querySelectorAll('.tab-content');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // 移除所有活动状态
            navLinks.forEach(l => l.classList.remove('active'));
            tabContents.forEach(t => t.classList.remove('active'));
            
            // 添加当前活动状态
            this.classList.add('active');
            const tabId = this.getAttribute('data-tab') + '-tab';
            const targetTab = document.getElementById(tabId);
            if (targetTab) {
                targetTab.classList.add('active');
            }
        });
    });
});

// 切换开关功能
function toggleSwitch(element) {
    element.classList.toggle('active');
}

// 浏览路径功能
function browsePath(inputElement) {
    currentBrowseTarget = inputElement || document.activeElement;
    showFolderBrowser();
}

function showFolderBrowser() {
    const modal = createModal();
    const content = createBrowserContent();
    modal.appendChild(content);
    document.body.appendChild(modal);

    // 设置事件委托
    setupEventDelegation();

    // 加载驱动器列表
    loadDrives();
}

function setupEventDelegation() {
    const folderList = document.getElementById('folder-list');
    console.log('设置事件委托，folderList:', folderList);
    if (folderList) {
        folderList.addEventListener('click', function(e) {
            console.log('文件夹列表被点击，目标:', e.target);
            const folderItem = e.target.closest('.folder-item');
            console.log('找到的文件夹项:', folderItem);
            if (folderItem && folderItem.dataset.path) {
                console.log('准备打开路径:', folderItem.dataset.path);
                openFolder(folderItem.dataset.path);
            } else {
                console.log('没有找到有效的路径');
            }
        });
    }
}

function createModal() {
    const modal = document.createElement('div');
    modal.className = 'folder-browser-modal';
    modal.style.cssText = 'position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.8); display: flex; align-items: center; justify-content: center; z-index: 10000;';
    
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.remove();
        }
    });
    
    return modal;
}

function createBrowserContent() {
    const content = document.createElement('div');
    content.style.cssText = 'background: #2d2d2d; border-radius: 8px; width: 600px; max-width: 90vw; max-height: 80vh; color: #ffffff; border: 1px solid #404040; display: flex; flex-direction: column;';
    
    content.innerHTML = [
        '<div class="browser-header" style="padding: 20px; border-bottom: 1px solid #404040; display: flex; justify-content: space-between; align-items: center;">',
        '<h3 style="margin: 0; color: #ffffff;">选择文件夹</h3>',
        '<button onclick="this.closest(\'.folder-browser-modal\').remove()" style="background: none; border: none; color: #888; font-size: 18px; cursor: pointer; padding: 4px;">X</button>',
        '</div>',
        '<div class="browser-toolbar" style="padding: 12px 20px; border-bottom: 1px solid #404040; display: flex; align-items: center; gap: 12px;">',
        '<button id="back-btn" onclick="goBack()" style="background: #404040; border: none; color: #e0e0e0; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 12px;" disabled>&lt; 返回</button>',
        '<button id="home-btn" onclick="goHome()" style="background: #4a9eff; border: none; color: white; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 12px;">驱动器</button>',
        '<div id="current-path" style="flex: 1; background: #1a1a1a; padding: 6px 12px; border-radius: 4px; font-family: monospace; font-size: 12px; color: #b0b0b0;">正在加载...</div>',
        '</div>',
        '<div class="browser-content" style="flex: 1; overflow-y: auto; min-height: 300px;">',
        '<div id="folder-list" style="padding: 12px;"></div>',
        '</div>',
        '<div class="browser-footer" style="padding: 16px 20px; border-top: 1px solid #404040; display: flex; justify-content: space-between; align-items: center;">',
        '<div id="selected-path" style="color: #b0b0b0; font-size: 12px; flex: 1;">请选择一个文件夹</div>',
        '<div style="display: flex; gap: 12px;">',
        '<button onclick="this.closest(\'.folder-browser-modal\').remove()" style="background: #555; color: #e0e0e0; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">取消</button>',
        '<button id="select-btn" onclick="selectFolder()" style="background: #4a9eff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;" disabled>选择</button>',
        '</div>',
        '</div>'
    ].join('');
    
    return content;
}

function loadDrives() {
    fetch('/system_settings/api/browse_drives')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayDrives(data.drives);
            } else {
                showError('无法加载驱动器列表: ' + data.error);
            }
        })
        .catch(error => {
            showError('加载驱动器失败: ' + error.message);
        });
}

function displayDrives(drives) {
    const folderList = document.getElementById('folder-list');
    const currentPathEl = document.getElementById('current-path');

    console.log('displayDrives 被调用，显示', drives.length, '个驱动器');

    currentPathEl.textContent = '选择驱动器';
    currentPath = '';

    let html = '<div style="margin-bottom: 12px; color: #4a9eff; font-weight: 500;">可用驱动器：</div>';
    
    drives.forEach(drive => {
        const sizeText = drive.total_space > 0 ? 
            formatBytes(drive.free_space) + ' 可用 / ' + formatBytes(drive.total_space) : '';
            
        html += '<div class="folder-item" data-path="' + drive.path + '" style="padding: 12px; margin: 4px 0; background: #404040; border-radius: 4px; cursor: pointer; transition: background 0.2s ease; display: flex; align-items: center; justify-content: space-between;" onmouseover="this.style.background=\'#4a4a4a\'" onmouseout="this.style.background=\'#404040\'">';
        html += '<div style="display: flex; align-items: center;">';
        html += '<i class="fas fa-hdd" style="margin-right: 12px; color: #4a9eff;"></i>';
        html += '<div>';
        html += '<div style="font-weight: 500;">' + drive.name + '</div>';
        if (sizeText) {
            html += '<div style="font-size: 11px; color: #888; margin-top: 2px;">' + sizeText + '</div>';
        }
        html += '</div>';
        html += '</div>';
        html += '<i class="fas fa-chevron-right" style="color: #888;"></i>';
        html += '</div>';
    });
    
    folderList.innerHTML = html;
    updateBackButton();
}

function openFolder(path) {
    console.log('openFolder 被调用，路径:', path);
    console.log('当前路径历史（添加前）:', pathHistory);

    // 添加当前路径到历史记录
    if (currentPath !== path) {
        pathHistory.push(currentPath);
        console.log('添加到历史记录:', currentPath);
    }

    openFolderWithoutHistory(path);
}

function openFolderWithoutHistory(path) {
    console.log('openFolderWithoutHistory 被调用，路径:', path);
    showLoading();

    fetch('/system_settings/api/browse_folder', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ path: path })
    })
    .then(response => {
        console.log('API响应状态:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('API响应数据:', data);
        if (data.success) {
            currentPath = data.current_path;
            displayFolders(data.folders, data.parent_path);
            updateCurrentPath(data.current_path);
            updateSelectedPath(data.current_path);
            updateBackButton();
        } else {
            showError('无法打开文件夹: ' + data.error);
        }
    })
    .catch(error => {
        console.error('openFolderWithoutHistory 错误:', error);
        showError('打开文件夹失败: ' + error.message);
    })
    .finally(() => {
        hideLoading();
    });
}

function displayFolders(folders, parentPath) {
    const folderList = document.getElementById('folder-list');
    
    let html = '';
    
    // 添加父目录选项
    if (parentPath) {
        html += '<div class="folder-item" data-path="' + parentPath + '" style="padding: 12px; margin: 4px 0; background: #404040; border-radius: 4px; cursor: pointer; transition: background 0.2s ease; display: flex; align-items: center;" onmouseover="this.style.background=\'#4a4a4a\'" onmouseout="this.style.background=\'#404040\'">';
        html += '<i class="fas fa-level-up-alt" style="margin-right: 12px; color: #888;"></i>';
        html += '<span style="color: #888;">.. (上级目录)</span>';
        html += '</div>';
    }
    
    // 添加文件夹列表
    folders.forEach(folder => {
        const accessibleClass = folder.accessible ? '' : 'opacity: 0.5;';
        const dataPath = folder.accessible ? 'data-path="' + folder.path + '"' : '';

        html += '<div class="folder-item" ' + dataPath + ' style="padding: 12px; margin: 4px 0; background: #404040; border-radius: 4px; ' + (folder.accessible ? 'cursor: pointer;' : 'cursor: not-allowed;') + ' transition: background 0.2s ease; display: flex; align-items: center; justify-content: space-between; ' + accessibleClass + '"' + (folder.accessible ? ' onmouseover="this.style.background=\'#4a4a4a\'" onmouseout="this.style.background=\'#404040\'"' : '') + '>';
        html += '<div style="display: flex; align-items: center;">';
        html += '<i class="fas fa-folder" style="margin-right: 12px; color: #ffa726;"></i>';
        html += '<span>' + folder.name + '</span>';
        html += '</div>';
        html += folder.accessible ? '<i class="fas fa-chevron-right" style="color: #888;"></i>' : '<i class="fas fa-lock" style="color: #f44336;"></i>';
        html += '</div>';
    });
    
    if (folders.length === 0) {
        html += '<div style="text-align: center; color: #888; padding: 20px;">此文件夹为空</div>';
    }
    
    folderList.innerHTML = html;
}

function goBack() {
    console.log('goBack 被调用，当前路径历史:', pathHistory);
    console.log('当前路径:', currentPath);

    if (pathHistory.length > 0) {
        const previousPath = pathHistory.pop();
        console.log('返回到路径:', previousPath);

        if (previousPath === '') {
            // 返回到驱动器选择界面
            currentPath = '';
            loadDrives();
        } else {
            // 返回到上一个文件夹，不要再添加到历史记录
            currentPath = previousPath;
            openFolderWithoutHistory(previousPath);
        }
    } else {
        // 如果没有历史记录，但当前不在驱动器选择界面，则返回到驱动器选择
        if (currentPath !== '') {
            console.log('没有历史记录，返回到驱动器选择');
            currentPath = '';
            loadDrives();
        }
    }
}

function goHome() {
    console.log('goHome 被调用，返回到驱动器选择');
    // 清空路径历史记录
    pathHistory = [];
    currentPath = '';
    loadDrives();
}

function updateCurrentPath(path) {
    const currentPathEl = document.getElementById('current-path');

    if (!path) {
        currentPathEl.textContent = '选择驱动器';
        return;
    }

    // 创建面包屑导航
    const parts = path.split(/[\\\/]/).filter(part => part);
    if (parts.length === 0) {
        currentPathEl.textContent = path;
        return;
    }

    // 如果路径太长，只显示最后几个部分
    const maxParts = 3;
    let displayParts = parts;
    if (parts.length > maxParts) {
        displayParts = ['...'].concat(parts.slice(-maxParts));
    }

    currentPathEl.textContent = displayParts.join(' > ');
}

function updateSelectedPath(path) {
    const selectedPathEl = document.getElementById('selected-path');
    const selectBtn = document.getElementById('select-btn');
    
    if (path) {
        selectedPathEl.textContent = '已选择: ' + path;
        selectBtn.disabled = false;
        selectBtn.style.opacity = '1';
    } else {
        selectedPathEl.textContent = '请选择一个文件夹';
        selectBtn.disabled = true;
        selectBtn.style.opacity = '0.5';
    }
}

function updateBackButton() {
    const backBtn = document.getElementById('back-btn');
    const homeBtn = document.getElementById('home-btn');

    console.log('updateBackButton - 路径历史长度:', pathHistory.length, '当前路径:', currentPath);

    // 如果有历史记录或者当前不在驱动器选择界面，则可以返回
    if (pathHistory.length > 0 || currentPath !== '') {
        backBtn.disabled = false;
        backBtn.style.opacity = '1';
    } else {
        backBtn.disabled = true;
        backBtn.style.opacity = '0.5';
    }

    // 如果当前在驱动器选择界面，隐藏"驱动器"按钮
    if (currentPath === '') {
        homeBtn.style.display = 'none';
    } else {
        homeBtn.style.display = 'inline-block';
    }
}

function selectFolder() {
    if (currentPath && currentBrowseTarget) {
        currentBrowseTarget.value = currentPath;
        
        // 触发输入事件以便表单验证
        const event = new Event('input', { bubbles: true });
        currentBrowseTarget.dispatchEvent(event);
        
        // 关闭浏览器
        document.querySelector('.folder-browser-modal').remove();
        
        showNotification('已选择路径: ' + currentPath, 'success');
    }
}

function showLoading() {
    const folderList = document.getElementById('folder-list');
    folderList.innerHTML = '<div style="text-align: center; padding: 40px; color: #888;"><i class="fas fa-spinner fa-spin" style="font-size: 24px; margin-bottom: 12px;"></i><div>正在加载...</div></div>';
}

function hideLoading() {
    // Loading state is handled by displayFolders or displayDrives
}

function showError(message) {
    const folderList = document.getElementById('folder-list');
    folderList.innerHTML = '<div style="text-align: center; padding: 40px; color: #f44336;"><i class="fas fa-exclamation-triangle" style="font-size: 24px; margin-bottom: 12px;"></i><div>' + message + '</div><button onclick="loadDrives()" style="margin-top: 12px; background: #4a9eff; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer;">重试</button></div>';
}

function formatBytes(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

// 重置表单功能
function resetForm() {
    if (confirm('确定要重置所有设置吗？这将恢复所有设置到默认值。')) {
        // 重置路径设置
        const documentPath = document.getElementById('document_path');
        const uploadPath = document.getElementById('upload_path');
        const maxFileSize = document.getElementById('max_file_size');

        if (documentPath) documentPath.value = 'D:/Documents/Materials';
        if (uploadPath) uploadPath.value = 'D:/QualitySystem/Images';
        if (maxFileSize) maxFileSize.value = '5';

        // 重置下拉选择
        const backupFrequency = document.querySelector('select.form-input');
        if (backupFrequency) backupFrequency.value = 'weekly';

        // 重置复选框
        const checkboxes = document.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach((cb, index) => {
            // 默认选中JPG和PNG格式
            cb.checked = index < 2;
        });

        // 重置所有开关到默认状态
        const switches = document.querySelectorAll('.toggle-switch');
        switches.forEach((sw, index) => {
            // 默认开启：自动保存、数据备份、路径自动创建、图片压缩、操作日志
            const defaultActive = [1, 2, 3, 4, 5]; // 对应开关的索引
            if (defaultActive.includes(index)) {
                sw.classList.add('active');
            } else {
                sw.classList.remove('active');
            }
        });

        showNotification('设置已重置为默认值', 'success');
    }
}

// 通知功能
function showNotification(message, type) {
    type = type || 'info';
    const notification = document.createElement('div');
    notification.style.cssText = 'position: fixed; top: 20px; right: 20px; padding: 12px 16px; border-radius: 6px; color: white; font-size: 14px; z-index: 10000; transform: translateX(100%); transition: transform 0.3s ease;';
    
    if (type === 'error') {
        notification.style.background = '#f44336';
    } else if (type === 'success') {
        notification.style.background = '#4caf50';
    } else {
        notification.style.background = '#4a9eff';
    }
    
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(function() {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    setTimeout(function() {
        notification.style.transform = 'translateX(100%)';
        setTimeout(function() {
            notification.remove();
        }, 300);
    }, 3000);
}

// 表单验证
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const documentPath = document.getElementById('document_path').value.trim();
            
            if (!documentPath) {
                showNotification('请输入文档文件夹路径', 'error');
                e.preventDefault();
                return false;
            }
            
            // 路径格式验证（更宽松的验证）
            const isWindowsPath = documentPath.match(/^[A-Za-z]:\\/);
            const isUNCPath = documentPath.match(/^\\\\/);
            const isRelativePath = !documentPath.match(/^[A-Za-z]:\\|^\\\\/);
            
            if (!isWindowsPath && !isUNCPath && !isRelativePath) {
                if (!confirm('输入的路径格式可能不正确，确定要保存吗？')) {
                    e.preventDefault();
                    return false;
                }
            }
            
            showNotification('正在保存设置...', 'info');
        });
    }
});
