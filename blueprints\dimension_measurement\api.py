#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
关键尺寸测量API
"""

from flask import jsonify, request
from . import dimension_measurement_bp
from db_config import get_db_connection
import json
from decimal import Decimal

@dimension_measurement_bp.route('/api/material_templates/<material_number>', methods=['GET'])
def get_material_dimension_templates(material_number):
    """获取物料的标准尺寸模板"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # 查询物料的标准尺寸模板
        cursor.execute("""
            SELECT id, sequence_no, position_name, nominal_dimension, 
                   upper_tolerance, lower_tolerance, dimension_range
            FROM material_dimension_templates 
            WHERE material_number = %s AND is_active = TRUE
            ORDER BY sequence_no
        """, (material_number,))
        
        templates = cursor.fetchall()
        
        # 转换Decimal为float以便JSON序列化，并处理NULL值
        for template in templates:
            for key, value in template.items():
                if isinstance(value, Decimal):
                    template[key] = float(value)
                elif value is None:
                    template[key] = None  # 保持None，让前端处理
        
        cursor.close()
        conn.close()
        
        return jsonify({
            "success": True, 
            "templates": templates,
            "count": len(templates)
        })
        
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@dimension_measurement_bp.route('/api/latest_measurements/<material_number>', methods=['GET'])
def get_latest_measurements(material_number):
    """获取物料最近一次的尺寸测量数据"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # 分步查询：先找到最近的检验记录ID，再查询对应的尺寸测量数据

        # 第一步：找到最近的检验记录
        cursor.execute("""
            (SELECT 'sampling' as type, id as inspection_id, inspection_date, created_at, report_code
             FROM sampling_inspection
             WHERE material_number = %s
             ORDER BY created_at DESC LIMIT 1)
            UNION ALL
            (SELECT 'full' as type, id as inspection_id, inspection_date, created_at, report_code
             FROM full_inspection
             WHERE material_number = %s
             ORDER BY created_at DESC LIMIT 1)
            ORDER BY created_at DESC LIMIT 1
        """, (material_number, material_number))

        latest_inspection = cursor.fetchone()



        if not latest_inspection:
            cursor.close()
            conn.close()
            return jsonify({
                "success": True,
                "measurements": [],
                "count": 0,
                "message": "未找到该物料的检验记录"
            })

        # 第二步：查询该检验记录的所有尺寸测量数据
        cursor.execute("""
            SELECT dm.*
            FROM dimension_measurements dm
            WHERE dm.material_number = %s
            AND dm.inspection_id = %s
            AND dm.inspection_type = %s
            ORDER BY dm.sequence_no ASC
        """, (
            material_number,
            latest_inspection['inspection_id'],
            latest_inspection['type']
        ))
        
        measurements = cursor.fetchall()



        # 转换Decimal为float以便JSON序列化，并处理NULL值
        for measurement in measurements:
            # 添加检验日期和报告编码
            measurement['inspection_date'] = latest_inspection['inspection_date']
            measurement['report_code'] = latest_inspection['report_code']

            for key, value in measurement.items():
                if isinstance(value, Decimal):
                    measurement[key] = float(value)
                elif value is None:
                    measurement[key] = None  # 保持None，让前端处理
        
        cursor.close()
        conn.close()
        
        return jsonify({
            "success": True, 
            "measurements": measurements,
            "count": len(measurements)
        })
        
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@dimension_measurement_bp.route('/api/save_measurements', methods=['POST'])
def save_dimension_measurements():
    """保存关键尺寸测量数据"""
    try:
        data = request.get_json()
        
        inspection_id = data.get('inspection_id')
        inspection_type = data.get('inspection_type')
        material_number = data.get('material_number')
        measurements = data.get('measurements', [])
        
        if not all([inspection_id, inspection_type, material_number]):
            return jsonify({"success": False, "error": "缺少必要参数"}), 400
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 删除该检验记录的旧测量数据
        cursor.execute("""
            DELETE FROM dimension_measurements 
            WHERE inspection_id = %s AND inspection_type = %s
        """, (inspection_id, inspection_type))
        
        # 插入新的测量数据
        for measurement in measurements:
            cursor.execute("""
                INSERT INTO dimension_measurements (
                    inspection_id, inspection_type, material_number, sequence_no,
                    position_name, nominal_dimension, upper_tolerance, lower_tolerance,
                    measured_value_1, measured_value_2, measured_value_3, 
                    measured_value_4, measured_value_5, result, remarks
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                inspection_id, inspection_type, material_number, measurement.get('sequence_no'),
                measurement.get('position_name'), measurement.get('nominal_dimension'),
                measurement.get('upper_tolerance'), measurement.get('lower_tolerance'),
                measurement.get('measured_value_1'), measurement.get('measured_value_2'),
                measurement.get('measured_value_3'), measurement.get('measured_value_4'),
                measurement.get('measured_value_5'), measurement.get('result'),
                measurement.get('remarks')
            ))
        
        conn.commit()
        cursor.close()
        conn.close()
        
        return jsonify({"success": True, "message": "尺寸测量数据保存成功"})
        
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@dimension_measurement_bp.route('/api/calculate_result', methods=['POST'])
def calculate_measurement_result():
    """计算测量结果"""
    try:
        data = request.get_json()
        
        nominal = float(data.get('nominal_dimension', 0))
        upper_tol = float(data.get('upper_tolerance', 0))
        lower_tol = float(data.get('lower_tolerance', 0))
        measured_values = [
            data.get('measured_value_1'),
            data.get('measured_value_2'),
            data.get('measured_value_3'),
            data.get('measured_value_4'),
            data.get('measured_value_5')
        ]
        
        # 计算尺寸范围
        min_value = nominal + lower_tol
        max_value = nominal + upper_tol
        
        # 检查所有实测值
        result = "合格"
        for value in measured_values:
            if value is not None and value != "":
                try:
                    measured = float(value)
                    if measured < min_value or measured > max_value:
                        result = "不合格"
                        break
                except (ValueError, TypeError):
                    continue
        
        return jsonify({
            "success": True,
            "result": result,
            "dimension_range": f"{min_value:.4f} ~ {max_value:.4f}"
        })
        
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@dimension_measurement_bp.route('/api/update_template', methods=['POST'])
def update_material_template():
    """更新或创建物料标准尺寸模板"""
    try:
        data = request.get_json()
        
        material_number = data.get('material_number')
        templates = data.get('templates', [])
        
        if not material_number:
            return jsonify({"success": False, "error": "缺少物料编号"}), 400
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 先删除该物料的旧模板
        cursor.execute("""
            DELETE FROM material_dimension_templates 
            WHERE material_number = %s
        """, (material_number,))
        
        # 插入新模板
        for template in templates:
            cursor.execute("""
                INSERT INTO material_dimension_templates (
                    material_number, sequence_no, position_name, 
                    nominal_dimension, upper_tolerance, lower_tolerance
                ) VALUES (%s, %s, %s, %s, %s, %s)
            """, (
                material_number, template.get('sequence_no'),
                template.get('position_name'), template.get('nominal_dimension'),
                template.get('upper_tolerance'), template.get('lower_tolerance')
            ))
        
        conn.commit()
        cursor.close()
        conn.close()
        
        return jsonify({"success": True, "message": "标准尺寸模板更新成功"})
        
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
