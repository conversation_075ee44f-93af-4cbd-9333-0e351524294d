{% extends "base.html" %}

{% block title %}新增车间异常记录 - 品质中心管理系统{% endblock %}

{% block extra_css %}
<style>
    .form-section {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    
    .form-row {
        display: flex;
        flex-wrap: wrap;
        margin-right: -15px;
        margin-left: -15px;
    }
    
    .form-group {
        margin-bottom: 1rem;
        padding-right: 15px;
        padding-left: 15px;
    }
    
    .form-group.col-md-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }
    
    .form-group.col-md-4 {
        flex: 0 0 33.333333%;
        max-width: 33.333333%;
    }
    
    .form-group.col-md-12 {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    @media (max-width: 768px) {
        .form-group.col-md-6,
        .form-group.col-md-4 {
            flex: 0 0 100%;
            max-width: 100%;
        }
    }
    
    .abnormal-details-container {
        border: 1px solid #ddd;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 15px;
    }
    
    .abnormal-item {
        margin-bottom: 10px;
        padding: 10px;
        border: 1px solid #eee;
        border-radius: 5px;
        position: relative;
    }
    
    .remove-item {
        position: absolute;
        right: 10px;
        top: 10px;
        color: #dc3545;
        cursor: pointer;
    }
    
    .add-item-btn {
        margin-bottom: 15px;
    }
    
    .preview-image {
        max-width: 200px;
        max-height: 200px;
        margin-top: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        display: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <h1>新增车间异常记录</h1>
</div>

<div class="content-container">
    <form id="abnormalForm" enctype="multipart/form-data">
        <div class="form-section">
            <h4>基本信息</h4>
            <div class="form-row">
                <div class="form-group col-md-6">
                    <label for="product_number">产品编号 <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="product_number" name="product_number" required>
                </div>
                <div class="form-group col-md-6">
                    <label for="product_name">产品名称 <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="product_name" name="product_name" required>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group col-md-4">
                    <label for="workshop">车间 <span class="text-danger">*</span></label>
                    <select class="form-control" id="workshop" name="workshop" required>
                        <option value="">请选择车间</option>
                        <option value="注塑车间">注塑车间</option>
                        <option value="组装车间">组装车间</option>
                        <option value="喷涂车间">喷涂车间</option>
                        <option value="包装车间">包装车间</option>
                    </select>
                </div>
                <div class="form-group col-md-4">
                    <label for="process_name">工序名称 <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="process_name" name="process_name" required>
                </div>
                <div class="form-group col-md-4">
                    <label for="abnormal_type">异常类型 <span class="text-danger">*</span></label>
                    <select class="form-control" id="abnormal_type" name="abnormal_type" required>
                        <option value="">请选择异常类型</option>
                        <option value="外观不良">外观不良</option>
                        <option value="尺寸超差">尺寸超差</option>
                        <option value="功能异常">功能异常</option>
                        <option value="材质问题">材质问题</option>
                        <option value="其他">其他</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group col-md-6">
                    <label for="record_date">记录日期 <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" id="record_date" name="record_date" required>
                </div>
                <div class="form-group col-md-6">
                    <label for="recorder">记录人 <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="recorder" name="recorder" required>
                </div>
            </div>
        </div>

        <div class="form-section">
            <h4>异常详情</h4>
            <div id="abnormalDetailsContainer" class="abnormal-details-container">
                <!-- 异常项将在这里动态添加 -->
            </div>
            <button type="button" id="addItemBtn" class="btn btn-secondary add-item-btn">
                <i class="fas fa-plus"></i> 添加异常项
            </button>
        </div>

        <div class="form-section">
            <h4>附加信息</h4>
            <div class="form-row">
                <div class="form-group col-md-12">
                    <label for="image">上传图片（可选）</label>
                    <input type="file" class="form-control-file" id="image" name="image" accept="image/*">
                    <img id="imagePreview" class="preview-image" src="#" alt="预览图">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group col-md-12">
                    <label for="remarks">备注</label>
                    <textarea class="form-control" id="remarks" name="remarks" rows="3"></textarea>
                </div>
            </div>
        </div>

        <div class="form-actions">
            <button type="submit" class="btn btn-primary">提交</button>
            <a href="{{ url_for('process_control.workshop_abnormal') }}" class="btn btn-secondary">取消</a>
        </div>
    </form>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // 初始化日期为今天
        document.getElementById('record_date').valueAsDate = new Date();
        
        // 添加异常项
        $('#addItemBtn').click(function() {
            const itemId = Date.now(); // 使用时间戳作为唯一ID
            const itemHtml = `
                <div class="abnormal-item" data-id="${itemId}">
                    <span class="remove-item"><i class="fas fa-times"></i></span>
                    <div class="form-group">
                        <label for="detail_${itemId}">异常描述</label>
                        <textarea class="form-control abnormal-detail" id="detail_${itemId}" rows="2"></textarea>
                    </div>
                </div>
            `;
            $('#abnormalDetailsContainer').append(itemHtml);
        });
        
        // 移除异常项
        $(document).on('click', '.remove-item', function() {
            $(this).closest('.abnormal-item').remove();
        });
        
        // 图片预览
        $('#image').change(function() {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    $('#imagePreview').attr('src', e.target.result).show();
                }
                reader.readAsDataURL(file);
            } else {
                $('#imagePreview').hide();
            }
        });
        
        // 表单提交
        $('#abnormalForm').submit(function(e) {
            e.preventDefault();
            
            // 收集异常详情
            const abnormalDetails = [];
            $('.abnormal-item').each(function() {
                const detail = $(this).find('.abnormal-detail').val().trim();
                if (detail) {
                    abnormalDetails.push({ description: detail });
                }
            });
            
            // 创建FormData对象
            const formData = new FormData(this);
            
            // 添加异常详情（JSON格式）
            formData.append('abnormal_details', JSON.stringify(abnormalDetails));
            
            // 发送请求
            $.ajax({
                url: '/process/api/add_abnormal',
                method: 'POST',
                data: formData,
                contentType: false,
                processData: false,
                success: function(response) {
                    if (response.success) {
                        alert('异常记录添加成功');
                        // 跳转到异常记录列表页
                        window.location.href = "{{ url_for('process_control.workshop_abnormal') }}";
                    } else {
                        alert('添加失败: ' + response.error);
                    }
                },
                error: function() {
                    alert('请求失败，请稍后再试');
                }
            });
        });
    });
</script>
{% endblock %} 