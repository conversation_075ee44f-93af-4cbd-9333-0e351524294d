<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件下载设置 - QMS系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 20px;
        }
        
        .header {
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        
        .header h1 {
            margin: 0;
            color: #333;
            font-size: 24px;
        }
        
        .header p {
            margin: 5px 0 0 0;
            color: #666;
            font-size: 14px;
        }
        
        .setting-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            background: #fafafa;
        }
        
        .setting-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        
        .form-input {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            font-family: monospace;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #1976d2;
            box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.1);
        }
        
        .form-help {
            margin-top: 5px;
            font-size: 12px;
            color: #666;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        
        .btn-primary {
            background: #1976d2;
            color: white;
        }
        
        .btn-primary:hover {
            background: #1565c0;
        }
        
        .btn-secondary {
            background: #f5f5f5;
            color: #666;
            border: 1px solid #ddd;
        }
        
        .btn-secondary:hover {
            background: #e0e0e0;
        }
        
        .btn-success {
            background: #4caf50;
            color: white;
        }
        
        .btn-success:hover {
            background: #45a049;
        }
        
        .current-setting {
            background: #e8f5e8;
            border: 1px solid #c8e6c9;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .current-setting-title {
            font-weight: 600;
            color: #2e7d32;
            margin-bottom: 8px;
        }
        
        .current-path {
            font-family: monospace;
            background: white;
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid #c8e6c9;
            color: #333;
        }
        
        .alert {
            padding: 12px 16px;
            border-radius: 4px;
            margin-bottom: 20px;
            display: none;
        }
        
        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .alert-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .alert-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .example-paths {
            background: #f0f7ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
        }
        
        .example-paths h4 {
            margin: 0 0 10px 0;
            color: #1976d2;
            font-size: 14px;
        }
        
        .example-paths ul {
            margin: 0;
            padding-left: 20px;
            font-size: 12px;
            line-height: 1.6;
        }
        
        .example-paths code {
            background: #f8f9fa;
            padding: 2px 4px;
            border-radius: 2px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-cog"></i> 文件下载设置</h1>
            <p>配置系统文件的本地下载路径，适用于局域网环境下的多用户文件管理</p>
        </div>
        
        <div id="alert-container"></div>
        
        <div class="setting-section">
            <div class="setting-title">
                <i class="fas fa-folder"></i>
                当前下载路径
            </div>
            
            <div class="current-setting">
                <div class="current-setting-title">系统当前使用的下载路径：</div>
                <div class="current-path" id="current-path">加载中...</div>
            </div>
            
            <div class="form-group">
                <label class="form-label" for="download-path">
                    <i class="fas fa-edit"></i> 设置新的下载路径
                </label>
                <input type="text" id="download-path" class="form-input" placeholder="例如：C:\QMS1\" />
                <div class="form-help">
                    请输入完整的文件夹路径。系统会自动创建不存在的文件夹。
                </div>
            </div>
            
            <div class="button-group">
                <button class="btn btn-primary" onclick="saveDownloadPath()">
                    <i class="fas fa-save"></i> 保存设置
                </button>
                <button class="btn btn-secondary" onclick="resetToDefault()">
                    <i class="fas fa-undo"></i> 恢复默认
                </button>
                <button class="btn btn-success" onclick="testPath()">
                    <i class="fas fa-check"></i> 测试路径
                </button>
            </div>
            
            <div class="example-paths">
                <h4><i class="fas fa-lightbulb"></i> 路径示例</h4>
                <ul>
                    <li><strong>Windows:</strong> <code>C:\QMS1\</code> 或 <code>D:\Documents\QMS\</code></li>
                    <li><strong>网络路径:</strong> <code>\\server\shared\QMS\</code></li>
                    <li><strong>Linux/Mac:</strong> <code>/opt/QMS1/</code> 或 <code>/home/<USER>/QMS/</code></li>
                </ul>
            </div>
        </div>
        
        <div class="setting-section">
            <div class="setting-title">
                <i class="fas fa-info-circle"></i>
                功能说明
            </div>
            
            <div style="font-size: 14px; line-height: 1.6; color: #333;">
                <p><strong>本地下载功能：</strong></p>
                <ul>
                    <li>文件会自动下载到指定的本地路径</li>
                    <li>下载完成后系统会尝试自动打开文件</li>
                    <li>适用于局域网环境，每个用户可以配置自己的下载路径</li>
                    <li>支持网络共享文件夹路径</li>
                </ul>
                
                <p><strong>注意事项：</strong></p>
                <ul>
                    <li>确保指定的路径具有读写权限</li>
                    <li>网络路径需要确保网络连接稳定</li>
                    <li>路径中不要包含特殊字符</li>
                    <li>建议使用专门的QMS文件夹进行管理</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 页面加载时获取当前设置
        document.addEventListener('DOMContentLoaded', function() {
            loadCurrentPath();
        });
        
        // 加载当前下载路径
        function loadCurrentPath() {
            fetch('/system_settings/api/get_download_path')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('current-path').textContent = data.download_path;
                    document.getElementById('download-path').value = data.download_path;
                } else {
                    showAlert('获取当前设置失败：' + data.error, 'error');
                }
            })
            .catch(error => {
                console.error('获取设置失败:', error);
                showAlert('获取当前设置失败，请刷新页面重试', 'error');
            });
        }
        
        // 保存下载路径
        function saveDownloadPath() {
            const path = document.getElementById('download-path').value.trim();
            
            if (!path) {
                showAlert('请输入下载路径', 'error');
                return;
            }
            
            fetch('/system_settings/api/set_download_path', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    download_path: path
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('下载路径设置成功！', 'success');
                    document.getElementById('current-path').textContent = data.download_path;
                    
                    // 尝试创建文件夹
                    createFolder(data.download_path);
                } else {
                    showAlert('设置失败：' + data.error, 'error');
                }
            })
            .catch(error => {
                console.error('设置失败:', error);
                showAlert('设置失败，请重试', 'error');
            });
        }
        
        // 创建文件夹
        function createFolder(folderPath) {
            fetch('/system_settings/api/create_download_folder', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    folder_path: folderPath
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('文件夹创建成功：' + folderPath, 'success');
                } else {
                    showAlert('文件夹创建失败：' + data.error, 'info');
                }
            })
            .catch(error => {
                console.error('创建文件夹失败:', error);
            });
        }
        
        // 恢复默认路径
        function resetToDefault() {
            const defaultPath = navigator.platform.indexOf('Win') !== -1 ? 'C:\\QMS1\\' : '/opt/QMS1/';
            document.getElementById('download-path').value = defaultPath;
            showAlert('已恢复为默认路径，请点击"保存设置"确认', 'info');
        }
        
        // 测试路径
        function testPath() {
            const path = document.getElementById('download-path').value.trim();
            
            if (!path) {
                showAlert('请先输入要测试的路径', 'error');
                return;
            }
            
            fetch('/system_settings/api/create_download_folder', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    folder_path: path
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('路径测试成功！文件夹可以正常创建和访问', 'success');
                } else {
                    showAlert('路径测试失败：' + data.error, 'error');
                }
            })
            .catch(error => {
                console.error('路径测试失败:', error);
                showAlert('路径测试失败，请检查路径是否正确', 'error');
            });
        }
        
        // 显示提示信息
        function showAlert(message, type) {
            const container = document.getElementById('alert-container');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            alert.style.display = 'block';
            
            container.innerHTML = '';
            container.appendChild(alert);
            
            // 3秒后自动隐藏
            setTimeout(() => {
                alert.style.display = 'none';
            }, 3000);
        }
    </script>
</body>
</html>
