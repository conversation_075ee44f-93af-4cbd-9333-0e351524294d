// QMS文件管理器 - 内容脚本
// 在QMS页面中注入功能，拦截下载请求

console.log('QMS文件管理器扩展已加载');

// 扩展状态
let extensionStatus = {
  localAppConnected: false,
  lastCheck: null,
  downloadCount: 0
};

// 初始化扩展
function initializeExtension() {
  // 检查本地应用连接状态
  checkLocalAppStatus();
  
  // 注入自定义脚本
  injectCustomScript();
  
  // 添加扩展状态指示器
  addStatusIndicator();
  
  // 监听页面上的下载事件
  interceptDownloadEvents();
  
  console.log('QMS文件管理器扩展初始化完成');
}

// 检查本地应用状态
function checkLocalAppStatus() {
  chrome.runtime.sendMessage({
    action: 'checkLocalApp'
  }, (response) => {
    if (response) {
      extensionStatus.localAppConnected = response.connected;
      extensionStatus.lastCheck = new Date().toISOString();
      updateStatusIndicator();
    }
  });
}

// 注入自定义脚本到页面
function injectCustomScript() {
  const script = document.createElement('script');
  script.src = chrome.runtime.getURL('injected.js');
  script.onload = function() {
    this.remove();
  };
  (document.head || document.documentElement).appendChild(script);
}

// 添加状态指示器
function addStatusIndicator() {
  // 创建状态指示器
  const indicator = document.createElement('div');
  indicator.id = 'qms-extension-indicator';
  indicator.style.cssText = `
    position: fixed;
    top: 10px;
    right: 10px;
    z-index: 10000;
    background: #4caf50;
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-family: Arial, sans-serif;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    cursor: pointer;
    transition: all 0.3s ease;
  `;
  indicator.innerHTML = `
    <i style="margin-right: 5px;">🔗</i>
    <span id="qms-status-text">QMS文件管理器</span>
  `;
  
  // 点击显示详细状态
  indicator.addEventListener('click', showDetailedStatus);
  
  document.body.appendChild(indicator);
  
  // 初始更新状态
  updateStatusIndicator();
}

// 更新状态指示器
function updateStatusIndicator() {
  const indicator = document.getElementById('qms-extension-indicator');
  const statusText = document.getElementById('qms-status-text');
  
  if (indicator && statusText) {
    if (extensionStatus.localAppConnected) {
      indicator.style.background = '#4caf50';
      statusText.textContent = 'QMS文件管理器 (已连接)';
    } else {
      indicator.style.background = '#ff9800';
      statusText.textContent = 'QMS文件管理器 (未连接)';
    }
  }
}

// 显示详细状态
function showDetailedStatus() {
  const statusInfo = `
QMS文件管理器状态:
• 本地应用: ${extensionStatus.localAppConnected ? '已连接' : '未连接'}
• 最后检查: ${extensionStatus.lastCheck ? new Date(extensionStatus.lastCheck).toLocaleTimeString() : '未检查'}
• 下载次数: ${extensionStatus.downloadCount}

${!extensionStatus.localAppConnected ? '\n请确保QMS文件管理器应用程序正在运行。' : ''}
  `;
  
  alert(statusInfo);
}

// 拦截下载事件
function interceptDownloadEvents() {
  // 监听来自注入脚本的消息
  window.addEventListener('message', (event) => {
    if (event.source !== window) return;
    
    if (event.data.type === 'QMS_DOWNLOAD_REQUEST') {
      handleDownloadRequest(event.data.payload);
    }
  });
  
  // 拦截所有附件下载链接的点击事件
  document.addEventListener('click', (event) => {
    const target = event.target;
    
    // 检查是否是下载链接
    if (isDownloadLink(target)) {
      event.preventDefault();
      event.stopPropagation();
      
      const downloadUrl = getDownloadUrl(target);
      const fileName = getFileName(target);
      
      if (downloadUrl && fileName) {
        handleDownloadRequest({
          url: downloadUrl,
          filename: fileName,
          source: 'link_click'
        });
      }
    }
  }, true);
}

// 判断是否是下载链接
function isDownloadLink(element) {
  if (!element) return false;
  
  // 检查href属性
  const href = element.href || element.getAttribute('href');
  if (href && href.includes('/download')) {
    return true;
  }
  
  // 检查onclick属性
  const onclick = element.getAttribute('onclick');
  if (onclick && (onclick.includes('openAttachment') || onclick.includes('download'))) {
    return true;
  }
  
  // 检查父元素
  const parent = element.parentElement;
  if (parent && isDownloadLink(parent)) {
    return true;
  }
  
  return false;
}

// 获取下载URL
function getDownloadUrl(element) {
  // 从href属性获取
  let url = element.href || element.getAttribute('href');
  
  if (!url) {
    // 从onclick属性解析
    const onclick = element.getAttribute('onclick');
    if (onclick) {
      const match = onclick.match(/['"]([^'"]*\/download[^'"]*)['"]/);
      if (match) {
        url = match[1];
      }
    }
  }
  
  // 确保URL是完整的
  if (url && !url.startsWith('http')) {
    const baseUrl = window.location.origin;
    url = baseUrl + (url.startsWith('/') ? url : '/' + url);
  }
  
  return url;
}

// 获取文件名
function getFileName(element) {
  // 尝试从多个地方获取文件名
  let fileName = element.getAttribute('data-filename') || 
                 element.getAttribute('title') ||
                 element.textContent.trim();
  
  // 如果没有找到，尝试从URL解析
  if (!fileName) {
    const url = getDownloadUrl(element);
    if (url) {
      const urlParts = url.split('/');
      fileName = urlParts[urlParts.length - 1];
    }
  }
  
  // 如果仍然没有，使用默认名称
  if (!fileName) {
    fileName = `attachment_${Date.now()}.file`;
  }
  
  return fileName;
}

// 处理下载请求
function handleDownloadRequest(downloadData) {
  console.log('处理下载请求:', downloadData);
  
  // 显示下载提示
  showDownloadNotification('正在准备下载...', 'info');
  
  // 发送到后台脚本
  chrome.runtime.sendMessage({
    action: 'downloadFile',
    data: downloadData
  }, (response) => {
    if (response && response.success) {
      extensionStatus.downloadCount++;
      
      if (response.fallback) {
        showDownloadNotification('文件已下载到浏览器默认文件夹', 'warning');
      } else {
        showDownloadNotification(`文件已下载到指定路径: ${response.file_path || '本地文件夹'}`, 'success');
      }
    } else {
      const errorMsg = response ? response.error : '下载失败';
      showDownloadNotification(`下载失败: ${errorMsg}`, 'error');
    }
  });
}

// 显示下载通知
function showDownloadNotification(message, type = 'info') {
  // 移除现有通知
  const existingNotification = document.getElementById('qms-download-notification');
  if (existingNotification) {
    existingNotification.remove();
  }
  
  // 创建新通知
  const notification = document.createElement('div');
  notification.id = 'qms-download-notification';
  
  const colors = {
    info: '#2196f3',
    success: '#4caf50',
    warning: '#ff9800',
    error: '#f44336'
  };
  
  notification.style.cssText = `
    position: fixed;
    top: 60px;
    right: 10px;
    z-index: 10001;
    background: ${colors[type]};
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-family: Arial, sans-serif;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    max-width: 300px;
    word-wrap: break-word;
    animation: slideIn 0.3s ease-out;
  `;
  
  notification.innerHTML = `
    <div style="display: flex; align-items: center;">
      <span style="margin-right: 8px;">${type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️'}</span>
      <span>${message}</span>
    </div>
  `;
  
  document.body.appendChild(notification);
  
  // 自动移除通知
  setTimeout(() => {
    if (notification.parentNode) {
      notification.style.animation = 'slideOut 0.3s ease-in';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.remove();
        }
      }, 300);
    }
  }, 5000);
}

// 监听来自后台脚本的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'showStatus') {
    showDetailedStatus();
  }
  
  if (request.action === 'downloadComplete') {
    showDownloadNotification('下载完成！', 'success');
  }
  
  if (request.action === 'updateStatus') {
    extensionStatus = { ...extensionStatus, ...request.status };
    updateStatusIndicator();
  }
});

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
  @keyframes slideIn {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  @keyframes slideOut {
    from {
      transform: translateX(0);
      opacity: 1;
    }
    to {
      transform: translateX(100%);
      opacity: 0;
    }
  }
`;
document.head.appendChild(style);

// 页面加载完成后初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeExtension);
} else {
  initializeExtension();
}

// 定期检查本地应用状态
setInterval(checkLocalAppStatus, 30000);
