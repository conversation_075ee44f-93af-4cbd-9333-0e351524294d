# Python缓存文件
__pycache__/
*.py[cod]
*$py.class

# 分发/打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 单元测试/覆盖率报告
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# 环境变量
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
*.log

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 临时文件
*.tmp
*.temp
temp/
tmp/

# 上传文件（保留目录结构但忽略内容）
static/uploads/*
!static/uploads/.gitkeep
!static/uploads/defect_images/
static/uploads/defect_images/*
!static/uploads/defect_images/.gitkeep

# 文档材料目录
DocumentsMaterials/*
!DocumentsMaterials/.gitkeep

# 测试和调试文件
*test*.html
*debug*.html
*demo*.html
test_*.py
debug_*.py
validate_*.py

# 迁移脚本（一次性使用）
migrate_*.py
add_*.sql
update_database_*.py
