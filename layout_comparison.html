<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>布局对比 - 品质中心管理系统</title>
    <link rel="stylesheet" href="static/css/style.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .comparison-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .layout-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #1976d2;
            border-bottom: 2px solid #1976d2;
            padding-bottom: 8px;
        }
        .demo-dropdown {
            position: relative;
            display: inline-block;
            margin: 10px 0;
        }
        .demo-dropdown-content {
            display: block;
            position: static;
            background-color: white;
            min-width: 600px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            border-radius: 4px;
            padding: 10px;
        }
        .toggle-btn {
            background: #1976d2;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .toggle-btn:hover {
            background: #1565c0;
        }
        .toggle-btn.active {
            background: #0d47a1;
        }

        /* 调试样式 - 显示列宽度 */
        .debug-mode .menu-cell {
            border: 2px solid red !important;
            position: relative;
        }

        .debug-mode .menu-cell::before {
            content: attr(data-width);
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            background: red;
            color: white;
            padding: 2px 6px;
            font-size: 10px;
            border-radius: 3px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="comparison-container">
        <h1>检验功能按钮布局对比</h1>

        <div style="margin: 20px 0; text-align: center;">
            <button class="toggle-btn" onclick="toggleDebugMode()">🔍 调试模式 - 显示列宽度</button>
            <button class="toggle-btn" onclick="measureWidths()">📏 测量实际宽度</button>
        </div>

        <div id="width-report" style="background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 10px 0; display: none;">
            <h4>宽度测量结果：</h4>
            <div id="width-details"></div>
        </div>
        
        <div class="layout-section">
            <div class="section-title">🔄 优化后的横向布局（推荐）</div>
            <p><strong>特点：</strong>严格四等分布局（25% × 4），间距均匀，视觉平衡</p>
            <p><strong>修复：</strong>使用 !important 强制宽度，添加 box-sizing: border-box，防止内容影响布局</p>
            
            <div class="demo-dropdown">
                <div class="demo-dropdown-content">
                    <div class="dropdown-header">
                        <h3>来料检验</h3>
                    </div>
                    <div class="menu-grid">
                        <div class="menu-row">
                            <div class="menu-cell">
                                <a href="#" class="menu-item heading-2">
                                    <i class="fas fa-random"></i> 抽样检验
                                </a>
                            </div>
                            <div class="menu-cell">
                                <a href="#" class="menu-item">检验记录</a>
                            </div>
                            <div class="menu-cell">
                                <a href="#" class="menu-item">新增检验</a>
                            </div>
                            <div class="menu-cell">
                                <a href="#" class="menu-item" title="点击后先进入待检清单页面">批量导入待检</a>
                            </div>
                        </div>
                        <div class="menu-row">
                            <div class="menu-cell">
                                <a href="#" class="menu-item heading-2">
                                    <i class="fas fa-clipboard-check"></i> 全部检验
                                </a>
                            </div>
                            <div class="menu-cell">
                                <a href="#" class="menu-item">检验记录</a>
                            </div>
                            <div class="menu-cell">
                                <a href="#" class="menu-item">新增检验</a>
                            </div>
                            <div class="menu-cell">
                                <a href="#" class="menu-item" title="点击后先进入待检清单页面">批量导入待检</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="layout-section">
            <div class="section-title">📱 纵向布局方案（对比）</div>
            <p><strong>特点：</strong>垂直排列，移动端友好，但占用更多空间</p>
            
            <div class="demo-dropdown">
                <div class="demo-dropdown-content">
                    <div class="dropdown-header">
                        <h3>来料检验</h3>
                    </div>
                    <div class="menu-grid vertical-layout">
                        <div class="menu-row">
                            <div class="menu-cell">
                                <a href="#" class="menu-item heading-2">
                                    <i class="fas fa-random"></i> 抽样检验
                                </a>
                            </div>
                            <div class="menu-cell">
                                <a href="#" class="menu-item">检验记录</a>
                            </div>
                            <div class="menu-cell">
                                <a href="#" class="menu-item">新增检验</a>
                            </div>
                            <div class="menu-cell">
                                <a href="#" class="menu-item">批量导入待检</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="layout-section">
            <div class="section-title">📊 布局对比总结</div>
            <table style="width: 100%; border-collapse: collapse; margin-top: 15px;">
                <thead>
                    <tr style="background-color: #f8f9fa;">
                        <th style="padding: 12px; border: 1px solid #dee2e6; text-align: left;">对比维度</th>
                        <th style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">横向布局</th>
                        <th style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">纵向布局</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="padding: 10px; border: 1px solid #dee2e6;">空间利用率</td>
                        <td style="padding: 10px; border: 1px solid #dee2e6; text-align: center; color: #28a745;">✅ 高</td>
                        <td style="padding: 10px; border: 1px solid #dee2e6; text-align: center; color: #dc3545;">❌ 低</td>
                    </tr>
                    <tr>
                        <td style="padding: 10px; border: 1px solid #dee2e6;">操作效率</td>
                        <td style="padding: 10px; border: 1px solid #dee2e6; text-align: center; color: #28a745;">✅ 高</td>
                        <td style="padding: 10px; border: 1px solid #dee2e6; text-align: center; color: #ffc107;">⚠️ 中</td>
                    </tr>
                    <tr>
                        <td style="padding: 10px; border: 1px solid #dee2e6;">移动端适配</td>
                        <td style="padding: 10px; border: 1px solid #dee2e6; text-align: center; color: #ffc107;">⚠️ 需响应式</td>
                        <td style="padding: 10px; border: 1px solid #dee2e6; text-align: center; color: #28a745;">✅ 友好</td>
                    </tr>
                    <tr>
                        <td style="padding: 10px; border: 1px solid #dee2e6;">视觉清晰度</td>
                        <td style="padding: 10px; border: 1px solid #dee2e6; text-align: center; color: #28a745;">✅ 紧凑清晰</td>
                        <td style="padding: 10px; border: 1px solid #dee2e6; text-align: center; color: #28a745;">✅ 层次分明</td>
                    </tr>
                    <tr>
                        <td style="padding: 10px; border: 1px solid #dee2e6;">扩展性</td>
                        <td style="padding: 10px; border: 1px solid #dee2e6; text-align: center; color: #ffc107;">⚠️ 有限</td>
                        <td style="padding: 10px; border: 1px solid #dee2e6; text-align: center; color: #28a745;">✅ 良好</td>
                    </tr>
                </tbody>
            </table>
            
            <div style="margin-top: 20px; padding: 15px; background-color: #e3f2fd; border-left: 4px solid #1976d2; border-radius: 4px;">
                <strong>💡 推荐结论：</strong> 采用优化后的横向布局，配合响应式设计在移动端自动转为纵向堆叠，既保持了桌面端的操作效率，又确保了移动端的用户体验。
            </div>

            <div style="margin-top: 15px; padding: 15px; background-color: #fff3cd; border-left: 4px solid #ffc107; border-radius: 4px;">
                <strong>🔄 导航流程优化：</strong> "批量导入待检"按钮现在会先跳转到待检清单页面，用户可以查看当前待检情况后，再点击页面上的"批量导入"按钮进行实际的批量导入操作。这样的流程更符合用户的使用习惯。
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/js/all.min.js"></script>

    <script>
        let debugMode = false;

        function toggleDebugMode() {
            debugMode = !debugMode;
            const container = document.querySelector('.comparison-container');

            if (debugMode) {
                container.classList.add('debug-mode');
                measureWidths();
            } else {
                container.classList.remove('debug-mode');
                document.getElementById('width-report').style.display = 'none';
            }
        }

        function measureWidths() {
            const menuCells = document.querySelectorAll('.menu-grid:not(.vertical-layout) .menu-cell');
            const widthReport = document.getElementById('width-report');
            const widthDetails = document.getElementById('width-details');

            let report = '<table style="width: 100%; border-collapse: collapse;">';
            report += '<tr style="background: #e3f2fd;"><th style="padding: 8px; border: 1px solid #ddd;">列</th><th style="padding: 8px; border: 1px solid #ddd;">内容</th><th style="padding: 8px; border: 1px solid #ddd;">实际宽度</th><th style="padding: 8px; border: 1px solid #ddd;">百分比</th><th style="padding: 8px; border: 1px solid #ddd;">状态</th></tr>';

            const containerWidth = document.querySelector('.menu-grid').offsetWidth;

            menuCells.forEach((cell, index) => {
                if (index < 4) { // 只测量第一行的4个单元格
                    const width = cell.offsetWidth;
                    const percentage = ((width / containerWidth) * 100).toFixed(2);
                    const content = cell.textContent.trim();
                    const isCorrect = Math.abs(percentage - 25) < 1; // 允许1%的误差

                    // 为调试模式添加宽度属性
                    cell.setAttribute('data-width', percentage + '%');

                    report += `<tr>
                        <td style="padding: 8px; border: 1px solid #ddd;">第${index + 1}列</td>
                        <td style="padding: 8px; border: 1px solid #ddd;">${content}</td>
                        <td style="padding: 8px; border: 1px solid #ddd;">${width}px</td>
                        <td style="padding: 8px; border: 1px solid #ddd;">${percentage}%</td>
                        <td style="padding: 8px; border: 1px solid #ddd; color: ${isCorrect ? '#28a745' : '#dc3545'};">
                            ${isCorrect ? '✅ 正确' : '❌ 偏差'}
                        </td>
                    </tr>`;
                }
            });

            report += '</table>';
            report += `<p style="margin-top: 10px;"><strong>容器总宽度：</strong>${containerWidth}px</p>`;

            widthDetails.innerHTML = report;
            widthReport.style.display = 'block';
        }
    </script>
</body>
</html>
