from flask import request, jsonify
from . import process_control_bp
from db_config import get_db_connection
import json
from datetime import datetime

# 获取异常记录详情API
@process_control_bp.route('/api/abnormal_details/<int:record_id>', methods=['GET'])
def get_abnormal_details(record_id):
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # 查询异常记录
        cursor.execute("""
            SELECT * FROM workshop_abnormal 
            WHERE id = %s
        """, (record_id,))
        
        record = cursor.fetchone()
        
        if record:
            # 如果异常详情是JSON字符串，转换为Python对象
            if 'abnormal_details' in record and record['abnormal_details']:
                try:
                    record['abnormal_details'] = json.loads(record['abnormal_details'])
                except:
                    record['abnormal_details'] = []
            
            return jsonify({"success": True, "record": record})
        else:
            return jsonify({"success": False, "error": "未找到记录"}), 404
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

# 更新异常记录状态API
@process_control_bp.route('/api/update_abnormal_status/<int:record_id>', methods=['POST'])
def update_abnormal_status(record_id):
    try:
        data = request.json
        new_status = data.get('status')
        
        if not new_status:
            return jsonify({"success": False, "error": "状态不能为空"}), 400
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 更新记录状态
        cursor.execute("""
            UPDATE workshop_abnormal 
            SET status = %s, updated_at = %s
            WHERE id = %s
        """, (new_status, datetime.now(), record_id))
        
        conn.commit()
        
        cursor.close()
        conn.close()
        
        return jsonify({"success": True, "message": "状态更新成功"})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

# 获取异常类型统计API
@process_control_bp.route('/api/abnormal_statistics', methods=['GET'])
def abnormal_statistics():
    try:
        # 获取时间范围参数
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # 构建查询条件
        conditions = []
        params = []
        
        if start_date:
            conditions.append("record_date >= %s")
            params.append(start_date)
        
        if end_date:
            conditions.append("record_date <= %s")
            params.append(end_date)
        
        # 组合查询条件
        where_clause = " AND ".join(conditions)
        if where_clause:
            where_clause = "WHERE " + where_clause
        
        # 查询异常类型统计
        query = f"""
            SELECT abnormal_type, COUNT(*) as count
            FROM workshop_abnormal
            {where_clause}
            GROUP BY abnormal_type
            ORDER BY count DESC
        """
        cursor.execute(query, params)
        type_stats = cursor.fetchall()
        
        # 查询车间统计
        query = f"""
            SELECT workshop, COUNT(*) as count
            FROM workshop_abnormal
            {where_clause}
            GROUP BY workshop
            ORDER BY count DESC
        """
        cursor.execute(query, params)
        workshop_stats = cursor.fetchall()
        
        # 查询状态统计
        query = f"""
            SELECT status, COUNT(*) as count
            FROM workshop_abnormal
            {where_clause}
            GROUP BY status
            ORDER BY count DESC
        """
        cursor.execute(query, params)
        status_stats = cursor.fetchall()
        
        cursor.close()
        conn.close()
        
        return jsonify({
            "success": True, 
            "type_stats": type_stats,
            "workshop_stats": workshop_stats,
            "status_stats": status_stats
        })
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500 