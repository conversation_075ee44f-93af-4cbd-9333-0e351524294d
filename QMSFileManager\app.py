#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QMS文件管理器 - 本地客户端应用
支持指定路径下载和自动打开文件
"""

import os
import sys
import json
import time
import threading
import subprocess
import platform
from datetime import datetime
from pathlib import Path
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import requests
from http.server import HTTPServer, BaseHTTPRequestHandler
import webbrowser

class QMSFileManager:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("QMS文件管理器")
        self.root.geometry("600x500")
        self.root.iconbitmap() if hasattr(self.root, 'iconbitmap') else None
        
        # 配置文件路径
        self.config_file = Path.home() / "QMSFileManager" / "config.json"
        self.config_file.parent.mkdir(exist_ok=True)
        
        # 默认配置
        self.config = {
            "download_path": "C:\\QMS1\\",
            "auto_open": True,
            "server_port": 8765,
            "server_host": "**************:5000"
        }
        
        # 加载配置
        self.load_config()
        
        # 创建下载目录
        Path(self.config["download_path"]).mkdir(parents=True, exist_ok=True)
        
        # 下载历史
        self.download_history = []
        
        # 创建界面
        self.create_ui()
        
        # 启动本地服务器
        self.start_local_server()
        
    def load_config(self):
        """加载配置文件"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    saved_config = json.load(f)
                    self.config.update(saved_config)
        except Exception as e:
            print(f"加载配置失败: {e}")
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置失败: {e}")
    
    def create_ui(self):
        """创建用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置区域
        config_frame = ttk.LabelFrame(main_frame, text="下载设置", padding="10")
        config_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 下载路径设置
        ttk.Label(config_frame, text="下载路径:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.path_var = tk.StringVar(value=self.config["download_path"])
        path_frame = ttk.Frame(config_frame)
        path_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0))
        
        ttk.Entry(path_frame, textvariable=self.path_var, width=40).grid(row=0, column=0, sticky=(tk.W, tk.E))
        ttk.Button(path_frame, text="浏览", command=self.browse_folder).grid(row=0, column=1, padx=(5, 0))
        path_frame.columnconfigure(0, weight=1)
        
        # 自动打开设置
        self.auto_open_var = tk.BooleanVar(value=self.config["auto_open"])
        ttk.Checkbutton(config_frame, text="下载完成后自动打开文件", 
                       variable=self.auto_open_var).grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=5)
        
        # 服务器设置
        ttk.Label(config_frame, text="QMS服务器:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.server_var = tk.StringVar(value=self.config["server_host"])
        ttk.Entry(config_frame, textvariable=self.server_var, width=30).grid(row=2, column=1, sticky=tk.W, padx=(10, 0))
        
        # 保存按钮
        ttk.Button(config_frame, text="保存设置", command=self.save_settings).grid(row=3, column=1, sticky=tk.E, pady=(10, 0))
        
        # 状态区域
        status_frame = ttk.LabelFrame(main_frame, text="运行状态", padding="10")
        status_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.status_var = tk.StringVar(value="就绪 - 等待下载请求")
        ttk.Label(status_frame, textvariable=self.status_var).grid(row=0, column=0, sticky=tk.W)
        
        # 本地服务器状态
        self.server_status_var = tk.StringVar(value=f"本地服务器: 端口 {self.config['server_port']}")
        ttk.Label(status_frame, textvariable=self.server_status_var).grid(row=1, column=0, sticky=tk.W)
        
        # 下载历史区域
        history_frame = ttk.LabelFrame(main_frame, text="下载历史", padding="10")
        history_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 创建Treeview
        columns = ("时间", "文件名", "状态", "路径")
        self.history_tree = ttk.Treeview(history_frame, columns=columns, show="headings", height=10)
        
        for col in columns:
            self.history_tree.heading(col, text=col)
            self.history_tree.column(col, width=120)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, command=self.history_tree.yview)
        self.history_tree.configure(yscrollcommand=scrollbar.set)
        
        self.history_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 操作按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        ttk.Button(button_frame, text="打开下载文件夹", command=self.open_download_folder).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(button_frame, text="清空历史", command=self.clear_history).grid(row=0, column=1, padx=5)
        ttk.Button(button_frame, text="安装浏览器扩展", command=self.install_extension).grid(row=0, column=2, padx=5)
        ttk.Button(button_frame, text="测试连接", command=self.test_connection).grid(row=0, column=3, padx=(5, 0))
        
        # 配置网格权重
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)
        config_frame.columnconfigure(1, weight=1)
        history_frame.columnconfigure(0, weight=1)
        history_frame.rowconfigure(0, weight=1)
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
    
    def browse_folder(self):
        """浏览文件夹"""
        folder = filedialog.askdirectory(initialdir=self.path_var.get())
        if folder:
            self.path_var.set(folder)
    
    def save_settings(self):
        """保存设置"""
        self.config["download_path"] = self.path_var.get()
        self.config["auto_open"] = self.auto_open_var.get()
        self.config["server_host"] = self.server_var.get()
        
        # 创建下载目录
        try:
            Path(self.config["download_path"]).mkdir(parents=True, exist_ok=True)
            self.save_config()
            self.status_var.set("设置已保存")
            messagebox.showinfo("成功", "设置已保存")
        except Exception as e:
            messagebox.showerror("错误", f"保存设置失败: {e}")
    
    def open_download_folder(self):
        """打开下载文件夹"""
        try:
            if platform.system() == 'Windows':
                os.startfile(self.config["download_path"])
            elif platform.system() == 'Darwin':  # macOS
                subprocess.run(['open', self.config["download_path"]])
            else:  # Linux
                subprocess.run(['xdg-open', self.config["download_path"]])
        except Exception as e:
            messagebox.showerror("错误", f"无法打开文件夹: {e}")
    
    def clear_history(self):
        """清空下载历史"""
        if messagebox.askyesno("确认", "确定要清空下载历史吗？"):
            for item in self.history_tree.get_children():
                self.history_tree.delete(item)
            self.download_history.clear()
    
    def install_extension(self):
        """安装浏览器扩展指导"""
        msg = """浏览器扩展安装指导：

1. 下载扩展文件包
2. 打开Chrome浏览器，进入扩展管理页面 (chrome://extensions/)
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择扩展文件夹

扩展安装后，网页上的下载按钮将自动与本应用程序通信。"""
        
        messagebox.showinfo("浏览器扩展安装", msg)
        
        # 打开扩展文件夹（如果存在）
        extension_path = Path(__file__).parent / "browser_extension"
        if extension_path.exists():
            self.open_folder(str(extension_path))
    
    def test_connection(self):
        """测试与QMS服务器的连接"""
        try:
            url = f"http://{self.config['server_host']}/system_settings/api/get_download_path"
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                self.status_var.set("服务器连接正常")
                messagebox.showinfo("连接测试", "与QMS服务器连接正常")
            else:
                self.status_var.set(f"服务器响应错误: {response.status_code}")
                messagebox.showwarning("连接测试", f"服务器响应错误: {response.status_code}")
        except Exception as e:
            self.status_var.set(f"连接失败: {e}")
            messagebox.showerror("连接测试", f"无法连接到QMS服务器: {e}")
    
    def open_folder(self, path):
        """打开文件夹"""
        try:
            if platform.system() == 'Windows':
                os.startfile(path)
            elif platform.system() == 'Darwin':
                subprocess.run(['open', path])
            else:
                subprocess.run(['xdg-open', path])
        except Exception as e:
            print(f"无法打开文件夹: {e}")
    
    def start_local_server(self):
        """启动本地HTTP服务器"""
        def run_server():
            try:
                server = HTTPServer(('localhost', self.config['server_port']), QMSRequestHandler)
                server.qms_app = self  # 传递应用实例
                print(f"本地服务器启动在端口 {self.config['server_port']}")
                server.serve_forever()
            except Exception as e:
                print(f"服务器启动失败: {e}")
                self.server_status_var.set(f"服务器启动失败: {e}")
        
        server_thread = threading.Thread(target=run_server, daemon=True)
        server_thread.start()
    
    def download_file(self, download_request):
        """下载文件"""
        try:
            url = download_request.get('url')
            filename = download_request.get('filename')
            
            if not url or not filename:
                return {"success": False, "error": "缺少URL或文件名"}
            
            # 更新状态
            self.status_var.set(f"正在下载: {filename}")
            
            # 下载文件
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            # 保存文件
            file_path = Path(self.config["download_path"]) / filename
            
            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            # 添加到历史记录
            self.add_to_history(filename, "下载成功", str(file_path))
            
            # 自动打开文件
            if self.config["auto_open"]:
                self.open_file(str(file_path))
            
            self.status_var.set("下载完成")
            
            return {
                "success": True,
                "file_path": str(file_path),
                "message": f"文件已下载到: {file_path}"
            }
            
        except Exception as e:
            error_msg = f"下载失败: {e}"
            self.status_var.set(error_msg)
            self.add_to_history(filename if 'filename' in locals() else "未知文件", "下载失败", error_msg)
            return {"success": False, "error": error_msg}
    
    def open_file(self, file_path):
        """打开文件"""
        try:
            if platform.system() == 'Windows':
                os.startfile(file_path)
            elif platform.system() == 'Darwin':
                subprocess.run(['open', file_path])
            else:
                subprocess.run(['xdg-open', file_path])
        except Exception as e:
            print(f"无法打开文件: {e}")
    
    def add_to_history(self, filename, status, path):
        """添加到下载历史"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.history_tree.insert("", 0, values=(timestamp, filename, status, path))
        
        # 限制历史记录数量
        children = self.history_tree.get_children()
        if len(children) > 100:
            self.history_tree.delete(children[-1])
    
    def run(self):
        """运行应用"""
        self.root.mainloop()

class QMSRequestHandler(BaseHTTPRequestHandler):
    """处理来自浏览器扩展的请求"""
    
    def do_POST(self):
        if self.path == '/download':
            try:
                content_length = int(self.headers['Content-Length'])
                post_data = self.rfile.read(content_length)
                request_data = json.loads(post_data.decode('utf-8'))
                
                # 处理下载请求
                result = self.server.qms_app.download_file(request_data)
                
                # 返回响应
                self.send_response(200)
                self.send_header('Content-Type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                
                response = json.dumps(result, ensure_ascii=False)
                self.wfile.write(response.encode('utf-8'))
                
            except Exception as e:
                self.send_error(500, f"处理请求失败: {e}")
        else:
            self.send_error(404, "Not Found")
    
    def do_OPTIONS(self):
        """处理CORS预检请求"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def log_message(self, format, *args):
        """禁用默认日志输出"""
        pass

if __name__ == "__main__":
    app = QMSFileManager()
    app.run()
